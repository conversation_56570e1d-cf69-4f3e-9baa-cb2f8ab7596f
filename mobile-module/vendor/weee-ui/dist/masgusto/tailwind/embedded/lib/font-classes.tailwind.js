
  /** 
   * Do not edit directly
   * Generated on Mon May 12 2025 18:14:30 GMT-0400 (Eastern Daylight Time)
   */
  

module.exports = {
  ".enki-display-5xl": {
    "letterSpacing": "var(--fd-5xl-tr)",
    "fontSize": "var(--fd-5xl-sz)",
    "fontWeight": "var(--fd-5xl-wt)",
    "lineHeight": "var(--fd-5xl-lh)"
  },
  ".enki-display-5xl-strong": {
    "letterSpacing": "var(--fd-5xl-tr)",
    "fontSize": "var(--fd-5xl-sz)",
    "lineHeight": "var(--fd-5xl-lh)",
    "fontWeight": "var(--fd-5xl-wtsg)"
  },
  ".enki-display-4xl": {
    "letterSpacing": "var(--fd-4xl-tr)",
    "fontSize": "var(--fd-4xl-sz)",
    "fontWeight": "var(--fd-4xl-wt)",
    "lineHeight": "var(--fd-4xl-lh)"
  },
  ".enki-display-4xl-strong": {
    "letterSpacing": "var(--fd-4xl-tr)",
    "fontSize": "var(--fd-4xl-sz)",
    "lineHeight": "var(--fd-4xl-lh)",
    "fontWeight": "var(--fd-4xl-wtsg)"
  },
  ".enki-display-3xl": {
    "letterSpacing": "var(--fd-3xl-tr)",
    "fontSize": "var(--fd-3xl-sz)",
    "fontWeight": "var(--fd-3xl-wt)",
    "lineHeight": "var(--fd-3xl-lh)"
  },
  ".enki-display-3xl-strong": {
    "letterSpacing": "var(--fd-3xl-tr)",
    "fontSize": "var(--fd-3xl-sz)",
    "lineHeight": "var(--fd-3xl-lh)",
    "fontWeight": "var(--fd-3xl-wtsg)"
  },
  ".enki-display-2xl": {
    "letterSpacing": "var(--fd-2xl-tr)",
    "fontSize": "var(--fd-2xl-sz)",
    "fontWeight": "var(--fd-2xl-wt)",
    "lineHeight": "var(--fd-2xl-lh)"
  },
  ".enki-display-2xl-strong": {
    "letterSpacing": "var(--fd-2xl-tr)",
    "fontSize": "var(--fd-2xl-sz)",
    "lineHeight": "var(--fd-2xl-lh)",
    "fontWeight": "var(--fd-2xl-wtsg)"
  },
  ".enki-display-xl": {
    "letterSpacing": "var(--fd-xl-tr)",
    "fontSize": "var(--fd-xl-sz)",
    "fontWeight": "var(--fd-xl-wt)",
    "lineHeight": "var(--fd-xl-lh)"
  },
  ".enki-display-xl-strong": {
    "letterSpacing": "var(--fd-xl-tr)",
    "fontSize": "var(--fd-xl-sz)",
    "lineHeight": "var(--fd-xl-lh)",
    "fontWeight": "var(--fd-xl-wtsg)"
  },
  ".enki-display-lg": {
    "letterSpacing": "var(--fd-lg-tr)",
    "fontSize": "var(--fd-lg-sz)",
    "fontWeight": "var(--fd-lg-wt)",
    "lineHeight": "var(--fd-lg-lh)"
  },
  ".enki-display-lg-strong": {
    "letterSpacing": "var(--fd-lg-tr)",
    "fontSize": "var(--fd-lg-sz)",
    "lineHeight": "var(--fd-lg-lh)",
    "fontWeight": "var(--fd-lg-wtsg)"
  },
  ".enki-display-sm": {
    "letterSpacing": "var(--fd-sm-tr)",
    "fontSize": "var(--fd-sm-sz)",
    "fontWeight": "var(--fd-sm-wt)",
    "lineHeight": "var(--fd-sm-lh)"
  },
  ".enki-display-sm-strong": {
    "letterSpacing": "var(--fd-sm-tr)",
    "fontSize": "var(--fd-sm-sz)",
    "lineHeight": "var(--fd-sm-lh)",
    "fontWeight": "var(--fd-sm-wtsg)"
  },
  ".enki-heading-5xl": {
    "letterSpacing": "var(--fh-5xl-tr)",
    "fontSize": "var(--fh-5xl-sz)",
    "fontWeight": "var(--fh-5xl-wt)",
    "lineHeight": "var(--fh-5xl-lh)"
  },
  ".enki-heading-5xl-strong": {
    "letterSpacing": "var(--fh-5xl-tr)",
    "fontSize": "var(--fh-5xl-sz)",
    "lineHeight": "var(--fh-5xl-lh)",
    "fontWeight": "var(--fh-5xl-wtsg)"
  },
  ".enki-heading-4xl": {
    "letterSpacing": "var(--fh-4xl-tr)",
    "fontSize": "var(--fh-4xl-sz)",
    "fontWeight": "var(--fh-4xl-wt)",
    "lineHeight": "var(--fh-4xl-lh)"
  },
  ".enki-heading-4xl-strong": {
    "letterSpacing": "var(--fh-4xl-tr)",
    "fontSize": "var(--fh-4xl-sz)",
    "lineHeight": "var(--fh-4xl-lh)",
    "fontWeight": "var(--fh-4xl-wtsg)"
  },
  ".enki-heading-3xl": {
    "letterSpacing": "var(--fh-3xl-tr)",
    "fontSize": "var(--fh-3xl-sz)",
    "fontWeight": "var(--fh-3xl-wt)",
    "lineHeight": "var(--fh-3xl-lh)"
  },
  ".enki-heading-3xl-strong": {
    "letterSpacing": "var(--fh-3xl-tr)",
    "fontSize": "var(--fh-3xl-sz)",
    "lineHeight": "var(--fh-3xl-lh)",
    "fontWeight": "var(--fh-3xl-wtsg)"
  },
  ".enki-heading-2xl": {
    "letterSpacing": "var(--fh-2xl-tr)",
    "fontSize": "var(--fh-2xl-sz)",
    "fontWeight": "var(--fh-2xl-wt)",
    "lineHeight": "var(--fh-2xl-lh)"
  },
  ".enki-heading-2xl-strong": {
    "letterSpacing": "var(--fh-2xl-tr)",
    "fontSize": "var(--fh-2xl-sz)",
    "lineHeight": "var(--fh-2xl-lh)",
    "fontWeight": "var(--fh-2xl-wtsg)"
  },
  ".enki-heading-xl": {
    "letterSpacing": "var(--fh-xl-tr)",
    "fontSize": "var(--fh-xl-sz)",
    "fontWeight": "var(--fh-xl-wt)",
    "lineHeight": "var(--fh-xl-lh)"
  },
  ".enki-heading-xl-strong": {
    "letterSpacing": "var(--fh-xl-tr)",
    "fontSize": "var(--fh-xl-sz)",
    "lineHeight": "var(--fh-xl-lh)",
    "fontWeight": "var(--fh-xl-wtsg)"
  },
  ".enki-heading-lg": {
    "letterSpacing": "var(--fh-lg-tr)",
    "fontSize": "var(--fh-lg-sz)",
    "fontWeight": "var(--fh-lg-wt)",
    "lineHeight": "var(--fh-lg-lh)"
  },
  ".enki-heading-lg-strong": {
    "letterSpacing": "var(--fh-lg-tr)",
    "fontSize": "var(--fh-lg-sz)",
    "lineHeight": "var(--fh-lg-lh)",
    "fontWeight": "var(--fh-lg-wtsg)"
  },
  ".enki-heading-sm": {
    "letterSpacing": "var(--fh-sm-tr)",
    "fontSize": "var(--fh-sm-sz)",
    "fontWeight": "var(--fh-sm-wt)",
    "lineHeight": "var(--fh-sm-lh)"
  },
  ".enki-heading-sm-strong": {
    "letterSpacing": "var(--fh-sm-tr)",
    "fontSize": "var(--fh-sm-sz)",
    "lineHeight": "var(--fh-sm-lh)",
    "fontWeight": "var(--fh-sm-wtsg)"
  },
  ".enki-body-2xl": {
    "letterSpacing": "var(--fb-2xl-tr)",
    "fontSize": "var(--fb-2xl-sz)",
    "fontWeight": "var(--fb-2xl-wt)",
    "lineHeight": "var(--fb-2xl-lh)"
  },
  ".enki-body-2xl-strong": {
    "letterSpacing": "var(--fb-2xl-tr)",
    "fontSize": "var(--fb-2xl-sz)",
    "lineHeight": "var(--fb-2xl-lh)",
    "fontWeight": "var(--fb-2xl-wtsg)"
  },
  ".enki-body-2xl-medium": {
    "letterSpacing": "var(--fb-2xl-tr)",
    "fontSize": "var(--fb-2xl-sz)",
    "lineHeight": "var(--fb-2xl-lh)",
    "fontWeight": "var(--fb-2xl-wtmd)"
  },
  ".enki-body-xl": {
    "letterSpacing": "var(--fb-xl-tr)",
    "fontSize": "var(--fb-xl-sz)",
    "fontWeight": "var(--fb-xl-wt)",
    "lineHeight": "var(--fb-xl-lh)"
  },
  ".enki-body-xl-strong": {
    "letterSpacing": "var(--fb-xl-tr)",
    "fontSize": "var(--fb-xl-sz)",
    "lineHeight": "var(--fb-xl-lh)",
    "fontWeight": "var(--fb-xl-wtsg)"
  },
  ".enki-body-xl-medium": {
    "letterSpacing": "var(--fb-xl-tr)",
    "fontSize": "var(--fb-xl-sz)",
    "lineHeight": "var(--fb-xl-lh)",
    "fontWeight": "var(--fb-xl-wtmd)"
  },
  ".enki-body-lg": {
    "letterSpacing": "var(--fb-lg-tr)",
    "fontSize": "var(--fb-lg-sz)",
    "fontWeight": "var(--fb-lg-wt)",
    "lineHeight": "var(--fb-lg-lh)"
  },
  ".enki-body-lg-strong": {
    "letterSpacing": "var(--fb-lg-tr)",
    "fontSize": "var(--fb-lg-sz)",
    "lineHeight": "var(--fb-lg-lh)",
    "fontWeight": "var(--fb-lg-wtsg)"
  },
  ".enki-body-lg-medium": {
    "letterSpacing": "var(--fb-lg-tr)",
    "fontSize": "var(--fb-lg-sz)",
    "lineHeight": "var(--fb-lg-lh)",
    "fontWeight": "var(--fb-lg-wtmd)"
  },
  ".enki-body-base": {
    "letterSpacing": "var(--fb-bs-tr)",
    "fontSize": "var(--fb-bs-sz)",
    "fontWeight": "var(--fb-bs-wt)",
    "lineHeight": "var(--fb-bs-lh)"
  },
  ".enki-body-base-strong": {
    "letterSpacing": "var(--fb-bs-tr)",
    "fontSize": "var(--fb-bs-sz)",
    "lineHeight": "var(--fb-bs-lh)",
    "fontWeight": "var(--fb-bs-wtsg)"
  },
  ".enki-body-base-medium": {
    "letterSpacing": "var(--fb-bs-tr)",
    "fontSize": "var(--fb-bs-sz)",
    "lineHeight": "var(--fb-bs-lh)",
    "fontWeight": "var(--fb-bs-wtmd)"
  },
  ".enki-body-sm": {
    "letterSpacing": "var(--fb-sm-tr)",
    "fontSize": "var(--fb-sm-sz)",
    "fontWeight": "var(--fb-sm-wt)",
    "lineHeight": "var(--fb-sm-lh)"
  },
  ".enki-body-sm-strong": {
    "letterSpacing": "var(--fb-sm-tr)",
    "fontSize": "var(--fb-sm-sz)",
    "lineHeight": "var(--fb-sm-lh)",
    "fontWeight": "var(--fb-sm-wtsg)"
  },
  ".enki-body-sm-medium": {
    "letterSpacing": "var(--fb-sm-tr)",
    "fontSize": "var(--fb-sm-sz)",
    "lineHeight": "var(--fb-sm-lh)",
    "fontWeight": "var(--fb-sm-wtmd)"
  },
  ".enki-body-xs": {
    "letterSpacing": "var(--fb-xs-tr)",
    "fontSize": "var(--fb-xs-sz)",
    "fontWeight": "var(--fb-xs-wt)",
    "lineHeight": "var(--fb-xs-lh)"
  },
  ".enki-body-xs-strong": {
    "letterSpacing": "var(--fb-xs-tr)",
    "fontSize": "var(--fb-xs-sz)",
    "lineHeight": "var(--fb-xs-lh)",
    "fontWeight": "var(--fb-xs-wtsg)"
  },
  ".enki-body-xs-medium": {
    "letterSpacing": "var(--fb-xs-tr)",
    "fontSize": "var(--fb-xs-sz)",
    "lineHeight": "var(--fb-xs-lh)",
    "fontWeight": "var(--fb-xs-wtmd)"
  },
  ".enki-body-2xs": {
    "letterSpacing": "var(--fb-2xs-tr)",
    "fontSize": "var(--fb-2xs-sz)",
    "fontWeight": "var(--fb-2xs-wt)",
    "lineHeight": "var(--fb-2xs-lh)"
  },
  ".enki-body-2xs-strong": {
    "letterSpacing": "var(--fb-2xs-tr)",
    "fontSize": "var(--fb-2xs-sz)",
    "lineHeight": "var(--fb-2xs-lh)",
    "fontWeight": "var(--fb-2xs-wtsg)"
  },
  ".enki-body-2xs-medium": {
    "letterSpacing": "var(--fb-2xs-tr)",
    "fontSize": "var(--fb-2xs-sz)",
    "lineHeight": "var(--fb-2xs-lh)",
    "fontWeight": "var(--fb-2xs-wtmd)"
  },
  ".enki-body-3xs": {
    "letterSpacing": "var(--fb-3xs-tr)",
    "fontSize": "var(--fb-3xs-sz)",
    "fontWeight": "var(--fb-3xs-wt)",
    "lineHeight": "var(--fb-3xs-lh)"
  },
  ".enki-body-3xs-strong": {
    "letterSpacing": "var(--fb-3xs-tr)",
    "fontSize": "var(--fb-3xs-sz)",
    "lineHeight": "var(--fb-3xs-lh)",
    "fontWeight": "var(--fb-3xs-wtsg)"
  },
  ".enki-body-3xs-medium": {
    "letterSpacing": "var(--fb-3xs-tr)",
    "fontSize": "var(--fb-3xs-sz)",
    "lineHeight": "var(--fb-3xs-lh)",
    "fontWeight": "var(--fb-3xs-wtmd)"
  }
}