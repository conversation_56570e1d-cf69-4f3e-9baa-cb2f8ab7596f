
/**
 * Do not edit directly
 * Generated on Mon May 12 2025 18:14:30 GMT-0400 (Eastern Daylight Time)
 * IMPORTANT: Make sure you import 'styles.{lang}.css' before loading this file if you are loading individually
 * FOR EXAMPLE: If this is 'fonts.cjk.css', import 'styles.cjk.css' before loading this file
 */
  

:root {
   --display-5xl-tracking: var(--font-tracking-base);
   --display-5xl-size: var(--font-size-7xl);
   --display-5xl-family: var(--font-family-latin-display);
   --display-5xl-weight: var(--font-weight-500-medium);
   --display-5xl-lineheight: var(--font-lineheight-100);
   --display-5xl-weightstrong: var(--font-weight-600-semibold);
   --display-4xl-tracking: var(--font-tracking-base);
   --display-4xl-size: var(--font-size-6xl);
   --display-4xl-family: var(--font-family-latin-display);
   --display-4xl-weight: var(--font-weight-500-medium);
   --display-4xl-lineheight: var(--font-lineheight-100);
   --display-4xl-weightstrong: var(--font-weight-600-semibold);
   --display-3xl-tracking: var(--font-tracking-base);
   --display-3xl-size: var(--font-size-5xl);
   --display-3xl-family: var(--font-family-latin-display);
   --display-3xl-weight: var(--font-weight-500-medium);
   --display-3xl-lineheight: var(--font-lineheight-100);
   --display-3xl-weightstrong: var(--font-weight-600-semibold);
   --display-2xl-tracking: var(--font-tracking-base);
   --display-2xl-size: var(--font-size-4xl);
   --display-2xl-family: var(--font-family-latin-display);
   --display-2xl-weight: var(--font-weight-500-medium);
   --display-2xl-lineheight: var(--font-lineheight-100);
   --display-2xl-weightstrong: var(--font-weight-600-semibold);
   --display-xl-tracking: var(--font-tracking-base);
   --display-xl-size: var(--font-size-3xl);
   --display-xl-family: var(--font-family-latin-display);
   --display-xl-weight: var(--font-weight-500-medium);
   --display-xl-lineheight: var(--font-lineheight-100);
   --display-xl-weightstrong: var(--font-weight-600-semibold);
   --display-lg-tracking: var(--font-tracking-base);
   --display-lg-size: var(--font-size-2xl);
   --display-lg-family: var(--font-family-latin-display);
   --display-lg-weight: var(--font-weight-500-medium);
   --display-lg-lineheight: var(--font-lineheight-100);
   --display-lg-weightstrong: var(--font-weight-600-semibold);
   --display-sm-tracking: var(--font-tracking-base);
   --display-sm-size: var(--font-size-xl);
   --display-sm-family: var(--font-family-latin-display);
   --display-sm-weight: var(--font-weight-500-medium);
   --display-sm-lineheight: var(--font-lineheight-100);
   --display-sm-weightstrong: var(--font-weight-600-semibold);
   --heading-5xl-tracking: var(--font-tracking-base);
   --heading-5xl-size: var(--font-size-6xl);
   --heading-5xl-family: var(--font-family-latin-heading);
   --heading-5xl-weight: var(--font-weight-500-medium);
   --heading-5xl-lineheight: var(--font-lineheight-110);
   --heading-5xl-weightstrong: var(--font-weight-600-semibold);
   --heading-4xl-tracking: var(--font-tracking-base);
   --heading-4xl-size: var(--font-size-5xl);
   --heading-4xl-family: var(--font-family-latin-heading);
   --heading-4xl-weight: var(--font-weight-500-medium);
   --heading-4xl-lineheight: var(--font-lineheight-110);
   --heading-4xl-weightstrong: var(--font-weight-600-semibold);
   --heading-3xl-tracking: var(--font-tracking-base);
   --heading-3xl-size: var(--font-size-4xl);
   --heading-3xl-family: var(--font-family-latin-heading);
   --heading-3xl-weight: var(--font-weight-500-medium);
   --heading-3xl-lineheight: var(--font-lineheight-110);
   --heading-3xl-weightstrong: var(--font-weight-600-semibold);
   --heading-2xl-tracking: var(--font-tracking-base);
   --heading-2xl-size: var(--font-size-3xl);
   --heading-2xl-family: var(--font-family-latin-heading);
   --heading-2xl-weight: var(--font-weight-500-medium);
   --heading-2xl-lineheight: var(--font-lineheight-110);
   --heading-2xl-weightstrong: var(--font-weight-600-semibold);
   --heading-xl-tracking: var(--font-tracking-base);
   --heading-xl-size: var(--font-size-2xl);
   --heading-xl-family: var(--font-family-latin-heading);
   --heading-xl-weight: var(--font-weight-500-medium);
   --heading-xl-lineheight: var(--font-lineheight-110);
   --heading-xl-weightstrong: var(--font-weight-500-medium);
   --heading-lg-tracking: var(--font-tracking-base);
   --heading-lg-size: var(--font-size-xl);
   --heading-lg-family: var(--font-family-latin-heading);
   --heading-lg-weight: var(--font-weight-500-medium);
   --heading-lg-lineheight: var(--font-lineheight-110);
   --heading-lg-weightstrong: var(--font-weight-600-semibold);
   --heading-sm-tracking: var(--font-tracking-base);
   --heading-sm-size: var(--font-size-lg);
   --heading-sm-family: var(--font-family-latin-heading);
   --heading-sm-weight: var(--font-weight-500-medium);
   --heading-sm-lineheight: var(--font-lineheight-110);
   --heading-sm-weightstrong: var(--font-weight-600-semibold);
   --body-2xl-tracking: var(--font-tracking-base);
   --body-2xl-size: var(--font-size-2xl);
   --body-2xl-family: var(--font-family-latin-body);
   --body-2xl-weight: var(--font-weight-400-regular);
   --body-2xl-lineheight: var(--font-lineheight-125);
   --body-2xl-weightstrong: var(--font-weight-600-semibold);
   --body-2xl-weightmedium: var(--font-weight-500-medium);
   --body-xl-tracking: var(--font-tracking-base);
   --body-xl-size: var(--font-size-xl);
   --body-xl-family: var(--font-family-latin-body);
   --body-xl-weight: var(--font-weight-400-regular);
   --body-xl-lineheight: var(--font-lineheight-125);
   --body-xl-weightstrong: var(--font-weight-600-semibold);
   --body-xl-weightmedium: var(--font-weight-500-medium);
   --body-lg-tracking: var(--font-tracking-base);
   --body-lg-size: var(--font-size-lg);
   --body-lg-family: var(--font-family-latin-body);
   --body-lg-weight: var(--font-weight-400-regular);
   --body-lg-lineheight: var(--font-lineheight-125);
   --body-lg-weightstrong: var(--font-weight-600-semibold);
   --body-lg-weightmedium: var(--font-weight-500-medium);
   --body-base-tracking: var(--font-tracking-base);
   --body-base-size: var(--font-size-base);
   --body-base-family: var(--font-family-latin-body);
   --body-base-weight: var(--font-weight-400-regular);
   --body-base-lineheight: var(--font-lineheight-125);
   --body-base-weightstrong: var(--font-weight-600-semibold);
   --body-base-weightmedium: var(--font-weight-500-medium);
   --body-sm-tracking: var(--font-tracking-base);
   --body-sm-size: var(--font-size-sm);
   --body-sm-family: var(--font-family-latin-body);
   --body-sm-weight: var(--font-weight-400-regular);
   --body-sm-lineheight: var(--font-lineheight-125);
   --body-sm-weightstrong: var(--font-weight-600-semibold);
   --body-sm-weightmedium: var(--font-weight-500-medium);
   --body-xs-tracking: var(--font-tracking-base);
   --body-xs-size: var(--font-size-xs);
   --body-xs-family: var(--font-family-latin-body);
   --body-xs-weight: var(--font-weight-400-regular);
   --body-xs-lineheight: var(--font-lineheight-125);
   --body-xs-weightstrong: var(--font-weight-600-semibold);
   --body-xs-weightmedium: var(--font-weight-500-medium);
   --body-2xs-tracking: var(--font-tracking-base);
   --body-2xs-size: var(--font-size-2xs);
   --body-2xs-family: var(--font-family-latin-body);
   --body-2xs-weight: var(--font-weight-400-regular);
   --body-2xs-lineheight: var(--font-lineheight-125);
   --body-2xs-weightstrong: var(--font-weight-600-semibold);
   --body-2xs-weightmedium: var(--font-weight-500-medium);
   --body-3xs-tracking: var(--font-tracking-base);
   --body-3xs-size: var(--font-size-3xs);
   --body-3xs-family: var(--font-family-latin-body);
   --body-3xs-weight: var(--font-weight-400-regular);
   --body-3xs-lineheight: var(--font-lineheight-125);
   --body-3xs-weightstrong: var(--font-weight-600-semibold);
   --body-3xs-weightmedium: var(--font-weight-500-medium);
}
body, html { font-family: var(--font-family-latin-main); -webkit-text-size-adjust: 100%; -moz-osx-font-smoothing: grayscale; }


