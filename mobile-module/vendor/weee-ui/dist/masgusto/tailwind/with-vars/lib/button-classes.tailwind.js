
  /** 
   * Do not edit directly
   * Generated on Mon May 12 2025 18:14:29 GMT-0400 (Eastern Daylight Time)
   */
  

module.exports = {
  ".enki-button-primary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "var(--color-btn-primary-bg)",
    "color": "var(--color-btn-primary-fg-default)",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "var(--style-filter-lighten-1-hover)"
      }
    },
    "&:active": {
      "filter": "var(--style-filter-lighten-1-pressed)"
    }
  },
  ".enki-button-secondary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "var(--color-btn-secondary-bg)",
    "color": "var(--color-btn-secondary-fg-default)",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "var(--style-filter-lighten-1-hover)"
      }
    },
    "&:active": {
      "filter": "var(--style-filter-lighten-1-pressed)"
    }
  },
  ".enki-button-tertiary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "var(--color-btn-tertiary-bg)",
    "color": "var(--color-btn-tertiary-fg-default)",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "var(--style-filter-darken-1-hover)"
      }
    },
    "&:active": {
      "filter": "var(--style-filter-darken-1-pressed)"
    }
  },
  ".enki-button-confirmation": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "var(--color-btn-confirmation-bg)",
    "color": "var(--color-btn-confirmation-fg-default)",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "var(--style-filter-darken-1-hover)"
      }
    },
    "&:active": {
      "filter": "var(--style-filter-darken-1-pressed)"
    }
  },
  ".enki-button-critical": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "var(--color-btn-critical-bg)",
    "color": "var(--color-btn-critical-fg-default)",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "var(--style-filter-darken-1-hover)"
      }
    },
    "&:active": {
      "filter": "var(--style-filter-darken-1-pressed)"
    }
  },
  ".enki-button-disabled": {
    "appearance": "none",
    "outline": "none",
    "cursor": "not-allowed",
    "backgroundColor": "var(--color-btn-disabled-bg)",
    "color": "var(--color-btn-disabled-fg-default)"
  }
}