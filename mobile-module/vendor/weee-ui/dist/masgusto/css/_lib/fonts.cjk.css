
/**
 * Do not edit directly
 * Generated on Mon May 12 2025 18:14:29 GMT-0400 (Eastern Daylight Time)
 * IMPORTANT: Make sure you import 'styles.{lang}.css' before loading this file if you are loading individually
 * FOR EXAMPLE: If this is 'fonts.cjk.css', import 'styles.cjk.css' before loading this file
 */
  

:root {
   --display-5xl-tracking: var(--font-tracking-widest);
   --display-5xl-size: var(--font-size-6xl);
   --display-5xl-family: var(--font-family-cjk-display);
   --display-5xl-weight: var(--font-weight-500-medium);
   --display-5xl-lineheight: var(--font-lineheight-125);
   --display-5xl-weightstrong: var(--font-weight-700-bold);
   --display-4xl-tracking: var(--font-tracking-widest);
   --display-4xl-size: var(--font-size-5xl);
   --display-4xl-family: var(--font-family-cjk-display);
   --display-4xl-weight: var(--font-weight-500-medium);
   --display-4xl-lineheight: var(--font-lineheight-125);
   --display-4xl-weightstrong: var(--font-weight-700-bold);
   --display-3xl-tracking: var(--font-tracking-widest);
   --display-3xl-size: var(--font-size-4xl);
   --display-3xl-family: var(--font-family-cjk-display);
   --display-3xl-weight: var(--font-weight-500-medium);
   --display-3xl-lineheight: var(--font-lineheight-125);
   --display-3xl-weightstrong: var(--font-weight-700-bold);
   --display-2xl-tracking: var(--font-tracking-widest);
   --display-2xl-size: var(--font-size-3xl);
   --display-2xl-family: var(--font-family-cjk-display);
   --display-2xl-weight: var(--font-weight-500-medium);
   --display-2xl-lineheight: var(--font-lineheight-125);
   --display-2xl-weightstrong: var(--font-weight-700-bold);
   --display-xl-tracking: var(--font-tracking-widest);
   --display-xl-size: var(--font-size-2xl);
   --display-xl-family: var(--font-family-cjk-display);
   --display-xl-weight: var(--font-weight-500-medium);
   --display-xl-lineheight: var(--font-lineheight-125);
   --display-xl-weightstrong: var(--font-weight-700-bold);
   --display-lg-tracking: var(--font-tracking-widest);
   --display-lg-size: var(--font-size-xl);
   --display-lg-family: var(--font-family-cjk-display);
   --display-lg-weight: var(--font-weight-500-medium);
   --display-lg-lineheight: var(--font-lineheight-125);
   --display-lg-weightstrong: var(--font-weight-700-bold);
   --display-sm-tracking: var(--font-tracking-widest);
   --display-sm-size: var(--font-size-lg);
   --display-sm-family: var(--font-family-cjk-display);
   --display-sm-weight: var(--font-weight-500-medium);
   --display-sm-lineheight: var(--font-lineheight-125);
   --display-sm-weightstrong: var(--font-weight-700-bold);
   --heading-5xl-tracking: var(--font-tracking-widest);
   --heading-5xl-size: var(--font-size-6xl);
   --heading-5xl-family: var(--font-family-cjk-heading);
   --heading-5xl-weight: var(--font-weight-500-medium);
   --heading-5xl-lineheight: var(--font-lineheight-125);
   --heading-5xl-weightstrong: var(--font-weight-700-bold);
   --heading-4xl-tracking: var(--font-tracking-widest);
   --heading-4xl-size: var(--font-size-5xl);
   --heading-4xl-family: var(--font-family-cjk-heading);
   --heading-4xl-weight: var(--font-weight-500-medium);
   --heading-4xl-lineheight: var(--font-lineheight-125);
   --heading-4xl-weightstrong: var(--font-weight-700-bold);
   --heading-3xl-tracking: var(--font-tracking-widest);
   --heading-3xl-size: var(--font-size-4xl);
   --heading-3xl-family: var(--font-family-cjk-heading);
   --heading-3xl-weight: var(--font-weight-500-medium);
   --heading-3xl-lineheight: var(--font-lineheight-125);
   --heading-3xl-weightstrong: var(--font-weight-700-bold);
   --heading-2xl-tracking: var(--font-tracking-widest);
   --heading-2xl-size: var(--font-size-3xl);
   --heading-2xl-family: var(--font-family-cjk-heading);
   --heading-2xl-weight: var(--font-weight-500-medium);
   --heading-2xl-lineheight: var(--font-lineheight-125);
   --heading-2xl-weightstrong: var(--font-weight-700-bold);
   --heading-xl-tracking: var(--font-tracking-widest);
   --heading-xl-size: var(--font-size-2xl);
   --heading-xl-family: var(--font-family-cjk-heading);
   --heading-xl-weight: var(--font-weight-500-medium);
   --heading-xl-lineheight: var(--font-lineheight-125);
   --heading-xl-weightstrong: var(--font-weight-700-bold);
   --heading-lg-tracking: var(--font-tracking-widest);
   --heading-lg-size: var(--font-size-xl);
   --heading-lg-family: var(--font-family-cjk-heading);
   --heading-lg-weight: var(--font-weight-500-medium);
   --heading-lg-lineheight: var(--font-lineheight-125);
   --heading-lg-weightstrong: var(--font-weight-700-bold);
   --heading-sm-tracking: var(--font-tracking-widest);
   --heading-sm-size: var(--font-size-lg);
   --heading-sm-family: var(--font-family-cjk-heading);
   --heading-sm-weight: var(--font-weight-500-medium);
   --heading-sm-lineheight: var(--font-lineheight-125);
   --heading-sm-weightstrong: var(--font-weight-700-bold);
   --body-2xl-tracking: var(--font-tracking-widest);
   --body-2xl-size: var(--font-size-2xl);
   --body-2xl-family: var(--font-family-cjk-body);
   --body-2xl-weight: var(--font-weight-400-regular);
   --body-2xl-lineheight: var(--font-lineheight-125);
   --body-2xl-weightstrong: var(--font-weight-700-bold);
   --body-2xl-weightmedium: var(--font-weight-500-medium);
   --body-xl-tracking: var(--font-tracking-widest);
   --body-xl-size: var(--font-size-xl);
   --body-xl-family: var(--font-family-cjk-body);
   --body-xl-weight: var(--font-weight-400-regular);
   --body-xl-lineheight: var(--font-lineheight-125);
   --body-xl-weightstrong: var(--font-weight-700-bold);
   --body-xl-weightmedium: var(--font-weight-500-medium);
   --body-lg-tracking: var(--font-tracking-widest);
   --body-lg-size: var(--font-size-lg);
   --body-lg-family: var(--font-family-cjk-body);
   --body-lg-weight: var(--font-weight-400-regular);
   --body-lg-lineheight: var(--font-lineheight-125);
   --body-lg-weightstrong: var(--font-weight-700-bold);
   --body-lg-weightmedium: var(--font-weight-500-medium);
   --body-base-tracking: var(--font-tracking-widest);
   --body-base-size: var(--font-size-base);
   --body-base-family: var(--font-family-cjk-body);
   --body-base-weight: var(--font-weight-400-regular);
   --body-base-lineheight: var(--font-lineheight-125);
   --body-base-weightstrong: var(--font-weight-700-bold);
   --body-base-weightmedium: var(--font-weight-500-medium);
   --body-sm-tracking: var(--font-tracking-widest);
   --body-sm-size: var(--font-size-sm);
   --body-sm-family: var(--font-family-cjk-body);
   --body-sm-weight: var(--font-weight-400-regular);
   --body-sm-lineheight: var(--font-lineheight-125);
   --body-sm-weightstrong: var(--font-weight-700-bold);
   --body-sm-weightmedium: var(--font-weight-500-medium);
   --body-xs-tracking: var(--font-tracking-widest);
   --body-xs-size: var(--font-size-xs);
   --body-xs-family: var(--font-family-cjk-body);
   --body-xs-weight: var(--font-weight-400-regular);
   --body-xs-lineheight: var(--font-lineheight-125);
   --body-xs-weightstrong: var(--font-weight-700-bold);
   --body-xs-weightmedium: var(--font-weight-500-medium);
   --body-2xs-tracking: var(--font-tracking-widest);
   --body-2xs-size: var(--font-size-2xs);
   --body-2xs-family: var(--font-family-cjk-body);
   --body-2xs-weight: var(--font-weight-400-regular);
   --body-2xs-lineheight: var(--font-lineheight-125);
   --body-2xs-weightstrong: var(--font-weight-700-bold);
   --body-2xs-weightmedium: var(--font-weight-500-medium);
   --body-3xs-tracking: var(--font-tracking-widest);
   --body-3xs-size: var(--font-size-3xs);
   --body-3xs-family: var(--font-family-cjk-body);
   --body-3xs-weight: var(--font-weight-400-regular);
   --body-3xs-lineheight: var(--font-lineheight-125);
   --body-3xs-weightstrong: var(--font-weight-700-bold);
   --body-3xs-weightmedium: var(--font-weight-500-medium);
}
body, html { font-family: var(--font-family-cjk-main); -webkit-text-size-adjust: 100%; -moz-osx-font-smoothing: grayscale; }


.enki-display-5xl {
   letter-spacing: var(--display-5xl-tracking);
   font-size: var(--display-5xl-size);
   font-family: var(--display-5xl-family);
   font-weight: var(--display-5xl-weight);
   line-height: var(--display-5xl-lineheight);
}

.enki-display-5xl-strong {
   letter-spacing: var(--display-5xl-tracking);
   font-size: var(--display-5xl-size);
   font-family: var(--display-5xl-family);
   line-height: var(--display-5xl-lineheight);
   font-weight: var(--display-5xl-weightstrong);
}

.enki-display-4xl {
   letter-spacing: var(--display-4xl-tracking);
   font-size: var(--display-4xl-size);
   font-family: var(--display-4xl-family);
   font-weight: var(--display-4xl-weight);
   line-height: var(--display-4xl-lineheight);
}

.enki-display-4xl-strong {
   letter-spacing: var(--display-4xl-tracking);
   font-size: var(--display-4xl-size);
   font-family: var(--display-4xl-family);
   line-height: var(--display-4xl-lineheight);
   font-weight: var(--display-4xl-weightstrong);
}

.enki-display-3xl {
   letter-spacing: var(--display-3xl-tracking);
   font-size: var(--display-3xl-size);
   font-family: var(--display-3xl-family);
   font-weight: var(--display-3xl-weight);
   line-height: var(--display-3xl-lineheight);
}

.enki-display-3xl-strong {
   letter-spacing: var(--display-3xl-tracking);
   font-size: var(--display-3xl-size);
   font-family: var(--display-3xl-family);
   line-height: var(--display-3xl-lineheight);
   font-weight: var(--display-3xl-weightstrong);
}

.enki-display-2xl {
   letter-spacing: var(--display-2xl-tracking);
   font-size: var(--display-2xl-size);
   font-family: var(--display-2xl-family);
   font-weight: var(--display-2xl-weight);
   line-height: var(--display-2xl-lineheight);
}

.enki-display-2xl-strong {
   letter-spacing: var(--display-2xl-tracking);
   font-size: var(--display-2xl-size);
   font-family: var(--display-2xl-family);
   line-height: var(--display-2xl-lineheight);
   font-weight: var(--display-2xl-weightstrong);
}

.enki-display-xl {
   letter-spacing: var(--display-xl-tracking);
   font-size: var(--display-xl-size);
   font-family: var(--display-xl-family);
   font-weight: var(--display-xl-weight);
   line-height: var(--display-xl-lineheight);
}

.enki-display-xl-strong {
   letter-spacing: var(--display-xl-tracking);
   font-size: var(--display-xl-size);
   font-family: var(--display-xl-family);
   line-height: var(--display-xl-lineheight);
   font-weight: var(--display-xl-weightstrong);
}

.enki-display-lg {
   letter-spacing: var(--display-lg-tracking);
   font-size: var(--display-lg-size);
   font-family: var(--display-lg-family);
   font-weight: var(--display-lg-weight);
   line-height: var(--display-lg-lineheight);
}

.enki-display-lg-strong {
   letter-spacing: var(--display-lg-tracking);
   font-size: var(--display-lg-size);
   font-family: var(--display-lg-family);
   line-height: var(--display-lg-lineheight);
   font-weight: var(--display-lg-weightstrong);
}

.enki-display-sm {
   letter-spacing: var(--display-sm-tracking);
   font-size: var(--display-sm-size);
   font-family: var(--display-sm-family);
   font-weight: var(--display-sm-weight);
   line-height: var(--display-sm-lineheight);
}

.enki-display-sm-strong {
   letter-spacing: var(--display-sm-tracking);
   font-size: var(--display-sm-size);
   font-family: var(--display-sm-family);
   line-height: var(--display-sm-lineheight);
   font-weight: var(--display-sm-weightstrong);
}

.enki-heading-5xl {
   letter-spacing: var(--heading-5xl-tracking);
   font-size: var(--heading-5xl-size);
   font-family: var(--heading-5xl-family);
   font-weight: var(--heading-5xl-weight);
   line-height: var(--heading-5xl-lineheight);
}

.enki-heading-5xl-strong {
   letter-spacing: var(--heading-5xl-tracking);
   font-size: var(--heading-5xl-size);
   font-family: var(--heading-5xl-family);
   line-height: var(--heading-5xl-lineheight);
   font-weight: var(--heading-5xl-weightstrong);
}

.enki-heading-4xl {
   letter-spacing: var(--heading-4xl-tracking);
   font-size: var(--heading-4xl-size);
   font-family: var(--heading-4xl-family);
   font-weight: var(--heading-4xl-weight);
   line-height: var(--heading-4xl-lineheight);
}

.enki-heading-4xl-strong {
   letter-spacing: var(--heading-4xl-tracking);
   font-size: var(--heading-4xl-size);
   font-family: var(--heading-4xl-family);
   line-height: var(--heading-4xl-lineheight);
   font-weight: var(--heading-4xl-weightstrong);
}

.enki-heading-3xl {
   letter-spacing: var(--heading-3xl-tracking);
   font-size: var(--heading-3xl-size);
   font-family: var(--heading-3xl-family);
   font-weight: var(--heading-3xl-weight);
   line-height: var(--heading-3xl-lineheight);
}

.enki-heading-3xl-strong {
   letter-spacing: var(--heading-3xl-tracking);
   font-size: var(--heading-3xl-size);
   font-family: var(--heading-3xl-family);
   line-height: var(--heading-3xl-lineheight);
   font-weight: var(--heading-3xl-weightstrong);
}

.enki-heading-2xl {
   letter-spacing: var(--heading-2xl-tracking);
   font-size: var(--heading-2xl-size);
   font-family: var(--heading-2xl-family);
   font-weight: var(--heading-2xl-weight);
   line-height: var(--heading-2xl-lineheight);
}

.enki-heading-2xl-strong {
   letter-spacing: var(--heading-2xl-tracking);
   font-size: var(--heading-2xl-size);
   font-family: var(--heading-2xl-family);
   line-height: var(--heading-2xl-lineheight);
   font-weight: var(--heading-2xl-weightstrong);
}

.enki-heading-xl {
   letter-spacing: var(--heading-xl-tracking);
   font-size: var(--heading-xl-size);
   font-family: var(--heading-xl-family);
   font-weight: var(--heading-xl-weight);
   line-height: var(--heading-xl-lineheight);
}

.enki-heading-xl-strong {
   letter-spacing: var(--heading-xl-tracking);
   font-size: var(--heading-xl-size);
   font-family: var(--heading-xl-family);
   line-height: var(--heading-xl-lineheight);
   font-weight: var(--heading-xl-weightstrong);
}

.enki-heading-lg {
   letter-spacing: var(--heading-lg-tracking);
   font-size: var(--heading-lg-size);
   font-family: var(--heading-lg-family);
   font-weight: var(--heading-lg-weight);
   line-height: var(--heading-lg-lineheight);
}

.enki-heading-lg-strong {
   letter-spacing: var(--heading-lg-tracking);
   font-size: var(--heading-lg-size);
   font-family: var(--heading-lg-family);
   line-height: var(--heading-lg-lineheight);
   font-weight: var(--heading-lg-weightstrong);
}

.enki-heading-sm {
   letter-spacing: var(--heading-sm-tracking);
   font-size: var(--heading-sm-size);
   font-family: var(--heading-sm-family);
   font-weight: var(--heading-sm-weight);
   line-height: var(--heading-sm-lineheight);
}

.enki-heading-sm-strong {
   letter-spacing: var(--heading-sm-tracking);
   font-size: var(--heading-sm-size);
   font-family: var(--heading-sm-family);
   line-height: var(--heading-sm-lineheight);
   font-weight: var(--heading-sm-weightstrong);
}

.enki-body-2xl {
   letter-spacing: var(--body-2xl-tracking);
   font-size: var(--body-2xl-size);
   font-family: var(--body-2xl-family);
   font-weight: var(--body-2xl-weight);
   line-height: var(--body-2xl-lineheight);
}

.enki-body-2xl-strong {
   letter-spacing: var(--body-2xl-tracking);
   font-size: var(--body-2xl-size);
   font-family: var(--body-2xl-family);
   line-height: var(--body-2xl-lineheight);
   font-weight: var(--body-2xl-weightstrong);
}

.enki-body-2xl-medium {
   letter-spacing: var(--body-2xl-tracking);
   font-size: var(--body-2xl-size);
   font-family: var(--body-2xl-family);
   line-height: var(--body-2xl-lineheight);
   font-weight: var(--body-2xl-weightmedium);
}

.enki-body-xl {
   letter-spacing: var(--body-xl-tracking);
   font-size: var(--body-xl-size);
   font-family: var(--body-xl-family);
   font-weight: var(--body-xl-weight);
   line-height: var(--body-xl-lineheight);
}

.enki-body-xl-strong {
   letter-spacing: var(--body-xl-tracking);
   font-size: var(--body-xl-size);
   font-family: var(--body-xl-family);
   line-height: var(--body-xl-lineheight);
   font-weight: var(--body-xl-weightstrong);
}

.enki-body-xl-medium {
   letter-spacing: var(--body-xl-tracking);
   font-size: var(--body-xl-size);
   font-family: var(--body-xl-family);
   line-height: var(--body-xl-lineheight);
   font-weight: var(--body-xl-weightmedium);
}

.enki-body-lg {
   letter-spacing: var(--body-lg-tracking);
   font-size: var(--body-lg-size);
   font-family: var(--body-lg-family);
   font-weight: var(--body-lg-weight);
   line-height: var(--body-lg-lineheight);
}

.enki-body-lg-strong {
   letter-spacing: var(--body-lg-tracking);
   font-size: var(--body-lg-size);
   font-family: var(--body-lg-family);
   line-height: var(--body-lg-lineheight);
   font-weight: var(--body-lg-weightstrong);
}

.enki-body-lg-medium {
   letter-spacing: var(--body-lg-tracking);
   font-size: var(--body-lg-size);
   font-family: var(--body-lg-family);
   line-height: var(--body-lg-lineheight);
   font-weight: var(--body-lg-weightmedium);
}

.enki-body-base {
   letter-spacing: var(--body-base-tracking);
   font-size: var(--body-base-size);
   font-family: var(--body-base-family);
   font-weight: var(--body-base-weight);
   line-height: var(--body-base-lineheight);
}

.enki-body-base-strong {
   letter-spacing: var(--body-base-tracking);
   font-size: var(--body-base-size);
   font-family: var(--body-base-family);
   line-height: var(--body-base-lineheight);
   font-weight: var(--body-base-weightstrong);
}

.enki-body-base-medium {
   letter-spacing: var(--body-base-tracking);
   font-size: var(--body-base-size);
   font-family: var(--body-base-family);
   line-height: var(--body-base-lineheight);
   font-weight: var(--body-base-weightmedium);
}

.enki-body-sm {
   letter-spacing: var(--body-sm-tracking);
   font-size: var(--body-sm-size);
   font-family: var(--body-sm-family);
   font-weight: var(--body-sm-weight);
   line-height: var(--body-sm-lineheight);
}

.enki-body-sm-strong {
   letter-spacing: var(--body-sm-tracking);
   font-size: var(--body-sm-size);
   font-family: var(--body-sm-family);
   line-height: var(--body-sm-lineheight);
   font-weight: var(--body-sm-weightstrong);
}

.enki-body-sm-medium {
   letter-spacing: var(--body-sm-tracking);
   font-size: var(--body-sm-size);
   font-family: var(--body-sm-family);
   line-height: var(--body-sm-lineheight);
   font-weight: var(--body-sm-weightmedium);
}

.enki-body-xs {
   letter-spacing: var(--body-xs-tracking);
   font-size: var(--body-xs-size);
   font-family: var(--body-xs-family);
   font-weight: var(--body-xs-weight);
   line-height: var(--body-xs-lineheight);
}

.enki-body-xs-strong {
   letter-spacing: var(--body-xs-tracking);
   font-size: var(--body-xs-size);
   font-family: var(--body-xs-family);
   line-height: var(--body-xs-lineheight);
   font-weight: var(--body-xs-weightstrong);
}

.enki-body-xs-medium {
   letter-spacing: var(--body-xs-tracking);
   font-size: var(--body-xs-size);
   font-family: var(--body-xs-family);
   line-height: var(--body-xs-lineheight);
   font-weight: var(--body-xs-weightmedium);
}

.enki-body-2xs {
   letter-spacing: var(--body-2xs-tracking);
   font-size: var(--body-2xs-size);
   font-family: var(--body-2xs-family);
   font-weight: var(--body-2xs-weight);
   line-height: var(--body-2xs-lineheight);
}

.enki-body-2xs-strong {
   letter-spacing: var(--body-2xs-tracking);
   font-size: var(--body-2xs-size);
   font-family: var(--body-2xs-family);
   line-height: var(--body-2xs-lineheight);
   font-weight: var(--body-2xs-weightstrong);
}

.enki-body-2xs-medium {
   letter-spacing: var(--body-2xs-tracking);
   font-size: var(--body-2xs-size);
   font-family: var(--body-2xs-family);
   line-height: var(--body-2xs-lineheight);
   font-weight: var(--body-2xs-weightmedium);
}

.enki-body-3xs {
   letter-spacing: var(--body-3xs-tracking);
   font-size: var(--body-3xs-size);
   font-family: var(--body-3xs-family);
   font-weight: var(--body-3xs-weight);
   line-height: var(--body-3xs-lineheight);
}

.enki-body-3xs-strong {
   letter-spacing: var(--body-3xs-tracking);
   font-size: var(--body-3xs-size);
   font-family: var(--body-3xs-family);
   line-height: var(--body-3xs-lineheight);
   font-weight: var(--body-3xs-weightstrong);
}

.enki-body-3xs-medium {
   letter-spacing: var(--body-3xs-tracking);
   font-size: var(--body-3xs-size);
   font-family: var(--body-3xs-family);
   line-height: var(--body-3xs-lineheight);
   font-weight: var(--body-3xs-weightmedium);
}