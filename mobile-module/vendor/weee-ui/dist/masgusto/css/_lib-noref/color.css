/**
 * Do not edit directly
 * Generated on Mon, 12 May 2025 22:14:29 GMT
 */

:root {
  --color-primary-1: #1f4f2d;
  --color-primary-2: #36c25b;
  --color-primary-3: #eef2fb;
  --color-primary-4: #ffffff;
  --color-primary-5: #111111;
  --color-secondary-base-1: #027fff;
  --color-secondary-base-2: #9654ff;
  --color-secondary-base-3: #f43c7e;
  --color-secondary-light-1: #c6e5ff;
  --color-secondary-light-2: #eedeff;
  --color-secondary-light-3: #fce1f8;
  --color-secondary-dark-1: #000f5d;
  --color-secondary-dark-2: #16003d;
  --color-secondary-dark-3: #84003f;
  --color-tertiary-base-1: #ec4143;
  --color-tertiary-base-2: #ff5026;
  --color-tertiary-base-3: #1eba9c;
  --color-tertiary-base-4: #2da34f;
  --color-tertiary-base-5: #ffd600;
  --color-tertiary-light-1: #ffeded;
  --color-tertiary-light-2: #ffd2b8;
  --color-tertiary-light-3: #def1ed;
  --color-tertiary-light-4: #a6e7b9;
  --color-tertiary-light-5: #fffd9d;
  --color-tertiary-dark-1: #890001;
  --color-tertiary-dark-2: #c02500;
  --color-tertiary-dark-3: #034743;
  --color-tertiary-dark-4: #1f4f2d;
  --color-tertiary-electric-1: #f2f500;
  --color-shade-neutral-base-1: #b0b0b0;
  --color-shade-neutral-base-2: #a7a7a7;
  --color-shade-neutral-base-3: #9e9e9e;
  --color-shade-neutral-base-4: #999999;
  --color-shade-neutral-base-5: #878787;
  --color-shade-neutral-base-6: #777777;
  --color-shade-neutral-base-7: #676668;
  --color-shade-neutral-light-1: #fafafa;
  --color-shade-neutral-light-2: #f3f3f3;
  --color-shade-neutral-light-3: #e2e2e2;
  --color-shade-neutral-light-4: #e2e2e2;
  --color-shade-neutral-light-5: #cccccc;
  --color-shade-neutral-light-6: #c3c3c3;
  --color-shade-neutral-light-7: #bbbbbb;
  --color-shade-neutral-dark-1: #4d4d4d;
  --color-shade-neutral-dark-2: #424242;
  --color-shade-neutral-dark-3: #3b3b3b;
  --color-shade-neutral-dark-4: #333333;
  --color-shade-neutral-dark-5: #252525;
  --color-shade-neutral-dark-6: #19181a;
  --color-shade-neutral-dark-7: #111111;
  --color-shade-cool-base-1: #b8bfd1;
  --color-shade-cool-base-2: #aab1c4;
  --color-shade-cool-base-3: #a1a8bc;
  --color-shade-cool-base-4: #9299ae;
  --color-shade-cool-base-5: #758296;
  --color-shade-cool-base-6: #63738b;
  --color-shade-cool-base-7: #52667d;
  --color-shade-cool-light-1: #f6f9fc;
  --color-shade-cool-light-2: #eef2fb;
  --color-shade-cool-light-3: #e8eef8;
  --color-shade-cool-light-4: #e4eaf5;
  --color-shade-cool-light-5: #dee4f3;
  --color-shade-cool-light-6: #d3daeb;
  --color-shade-cool-light-7: #c7cede;
  --color-shade-cool-dark-1: #4c6078;
  --color-shade-cool-dark-2: #45586e;
  --color-shade-cool-dark-3: #3a4c60;
  --color-shade-cool-dark-4: #2b3948;
  --color-shade-cool-dark-5: #212f3d;
  --color-shade-cool-dark-6: #182432;
  --color-shade-cool-dark-7: #07101a;
  --color-root-mandarin-orange-base-1: #ff8263;
  --color-root-mandarin-orange-base-2: #ff7247;
  --color-root-mandarin-orange-base-3: #ff5f2e;
  --color-root-mandarin-orange-base-4: #ff5026;
  --color-root-mandarin-orange-base-5: #ff4214;
  --color-root-mandarin-orange-base-6: #f74216;
  --color-root-mandarin-orange-base-7: #e53d14;
  --color-root-mandarin-orange-light-1: #feefdb;
  --color-root-mandarin-orange-light-2: #ffd2b8;
  --color-root-mandarin-orange-light-3: #ffa08a;
  --color-root-mandarin-orange-light-4: #ff9278;
  --color-root-mandarin-orange-light-5: #ff8669;
  --color-root-mandarin-orange-light-6: #ff7859;
  --color-root-mandarin-orange-light-7: #fa7050;
  --color-root-mandarin-orange-dark-1: #e23309;
  --color-root-mandarin-orange-dark-2: #ce2800;
  --color-root-mandarin-orange-dark-3: #c02500;
  --color-root-mandarin-orange-dark-4: #af2200;
  --color-root-mandarin-orange-dark-5: #9b1e00;
  --color-root-mandarin-orange-dark-6: #852007;
  --color-root-mandarin-orange-dark-7: #592007;
  --color-root-flow-teal-base-1: #00ccff;
  --color-root-flow-teal-base-2: #00c0fc;
  --color-root-flow-teal-base-3: #00b8f2;
  --color-root-flow-teal-base-4: #00b2ea;
  --color-root-flow-teal-base-5: #00ace3;
  --color-root-flow-teal-base-6: #00a5d9;
  --color-root-flow-teal-base-7: #009bcc;
  --color-root-flow-teal-light-1: #eafaff;
  --color-root-flow-teal-light-2: #c4f2ff;
  --color-root-flow-teal-light-3: #a9ebff;
  --color-root-flow-teal-light-4: #94e5ff;
  --color-root-flow-teal-light-5: #80dffd;
  --color-root-flow-teal-light-6: #74d6f6;
  --color-root-flow-teal-light-7: #51cdf4;
  --color-root-flow-teal-dark-1: #40b5d9;
  --color-root-flow-teal-dark-2: #158baf;
  --color-root-flow-teal-dark-3: #097b9e;
  --color-root-flow-teal-dark-4: #006b8c;
  --color-root-flow-teal-dark-5: #08637f;
  --color-root-flow-teal-dark-6: #0a5972;
  --color-root-flow-teal-dark-7: #063847;
  --color-root-chive-green-base-1: #36c25b;
  --color-root-chive-green-base-2: #33b859;
  --color-root-chive-green-base-3: #30ad54;
  --color-root-chive-green-base-4: #2da34f;
  --color-root-chive-green-base-5: #329c50;
  --color-root-chive-green-base-6: #32944e;
  --color-root-chive-green-base-7: #32874a;
  --color-root-chive-green-light-1: #d7fae1;
  --color-root-chive-green-light-2: #b6f0c7;
  --color-root-chive-green-light-3: #a6e7b9;
  --color-root-chive-green-light-4: #92e0a9;
  --color-root-chive-green-light-5: #8ed4a2;
  --color-root-chive-green-light-6: #8dcc9f;
  --color-root-chive-green-light-7: #86bf97;
  --color-root-chive-green-dark-1: #4c8f59;
  --color-root-chive-green-dark-2: #427d48;
  --color-root-chive-green-dark-3: #386e3c;
  --color-root-chive-green-dark-4: #2b6633;
  --color-root-chive-green-dark-5: #275936;
  --color-root-chive-green-dark-6: #1f4f2d;
  --color-root-chive-green-dark-7: #1f4f2d;
  --color-root-energy-blue-base-1: #12acff;
  --color-root-energy-blue-base-2: #0898ff;
  --color-root-energy-blue-base-3: #088cff;
  --color-root-energy-blue-base-4: #027fff;
  --color-root-energy-blue-base-5: #0274ff;
  --color-root-energy-blue-base-6: #0865fc;
  --color-root-energy-blue-base-7: #075ce5;
  --color-root-energy-blue-light-1: #e9f2f5;
  --color-root-energy-blue-light-2: #dcf0f7;
  --color-root-energy-blue-light-3: #d1eaff;
  --color-root-energy-blue-light-4: #c6e5ff;
  --color-root-energy-blue-light-5: #baddff;
  --color-root-energy-blue-light-6: #b1d4fc;
  --color-root-energy-blue-light-7: #9ecbf7;
  --color-root-energy-blue-dark-1: #0c50cf;
  --color-root-energy-blue-dark-2: #0b42b8;
  --color-root-energy-blue-dark-3: #0030ab;
  --color-root-energy-blue-dark-4: #001ba5;
  --color-root-energy-blue-dark-5: #00108a;
  --color-root-energy-blue-dark-6: #000f5d;
  --color-root-energy-blue-dark-7: #000a3b;
  --color-root-jade-green-base-1: #30deba;
  --color-root-jade-green-base-2: #2ad2af;
  --color-root-jade-green-base-3: #24c6a6;
  --color-root-jade-green-base-4: #1eba9c;
  --color-root-jade-green-base-5: #1ab393;
  --color-root-jade-green-base-6: #16ab8a;
  --color-root-jade-green-base-7: #11a07d;
  --color-root-jade-green-light-1: #f3fefb;
  --color-root-jade-green-light-2: #def1ed;
  --color-root-jade-green-light-3: #b8e9df;
  --color-root-jade-green-light-4: #60e8ce;
  --color-root-jade-green-light-5: #4fdbbe;
  --color-root-jade-green-light-6: #3eceae;
  --color-root-jade-green-light-7: #2dc39e;
  --color-root-jade-green-dark-1: #018260;
  --color-root-jade-green-dark-2: #02755b;
  --color-root-jade-green-dark-3: #026d55;
  --color-root-jade-green-dark-4: #026150;
  --color-root-jade-green-dark-5: #035449;
  --color-root-jade-green-dark-6: #034743;
  --color-root-jade-green-dark-7: #023d36;
  --color-root-dragonfruit-pink-base-1: #ff8bb4;
  --color-root-dragonfruit-pink-base-2: #f876a5;
  --color-root-dragonfruit-pink-base-3: #f45991;
  --color-root-dragonfruit-pink-base-4: #f43c7e;
  --color-root-dragonfruit-pink-base-5: #ee206a;
  --color-root-dragonfruit-pink-base-6: #de2b6b;
  --color-root-dragonfruit-pink-base-7: #d02361;
  --color-root-dragonfruit-pink-light-1: #fff9fe;
  --color-root-dragonfruit-pink-light-2: #fff4fd;
  --color-root-dragonfruit-pink-light-3: #ffecfc;
  --color-root-dragonfruit-pink-light-4: #fce1f8;
  --color-root-dragonfruit-pink-light-5: #ffc7f7;
  --color-root-dragonfruit-pink-light-6: #f6a0ea;
  --color-root-dragonfruit-pink-light-7: #f580e5;
  --color-root-dragonfruit-pink-dark-1: #cb0262;
  --color-root-dragonfruit-pink-dark-2: #b60057;
  --color-root-dragonfruit-pink-dark-3: #9d014c;
  --color-root-dragonfruit-pink-dark-4: #84003f;
  --color-root-dragonfruit-pink-dark-5: #6e0738;
  --color-root-dragonfruit-pink-dark-6: #5a1033;
  --color-root-dragonfruit-pink-dark-7: #4e0729;
  --color-root-durian-yellow-base-1: #ffed8e;
  --color-root-durian-yellow-base-2: #ffe662;
  --color-root-durian-yellow-base-3: #ffde2e;
  --color-root-durian-yellow-base-4: #ffd600;
  --color-root-durian-yellow-base-5: #ffcc00;
  --color-root-durian-yellow-base-6: #ffc300;
  --color-root-durian-yellow-base-7: #ffb700;
  --color-root-durian-yellow-light-1: #fffec4;
  --color-root-durian-yellow-light-2: #fffd9d;
  --color-root-durian-yellow-light-3: #fffc63;
  --color-root-durian-yellow-light-4: #fff858;
  --color-root-durian-yellow-light-5: #fcf428;
  --color-root-durian-yellow-light-6: #fdeb28;
  --color-root-durian-yellow-light-7: #ffe23d;
  --color-root-durian-yellow-dark-1: #ab8403;
  --color-root-durian-yellow-dark-2: #a67605;
  --color-root-durian-yellow-dark-3: #a66c08;
  --color-root-durian-yellow-dark-4: #a36303;
  --color-root-durian-yellow-dark-5: #9e5c00;
  --color-root-durian-yellow-dark-6: #7d4c02;
  --color-root-durian-yellow-dark-7: #573f1d;
  --color-root-tomato-red-base-1: #ff7072;
  --color-root-tomato-red-base-2: #ff6062;
  --color-root-tomato-red-base-3: #f94648;
  --color-root-tomato-red-base-4: #ec4143;
  --color-root-tomato-red-base-5: #ec2e3d;
  --color-root-tomato-red-base-6: #e32c3b;
  --color-root-tomato-red-base-7: #d92a38;
  --color-root-tomato-red-light-1: #ffeded;
  --color-root-tomato-red-light-2: #ffe1e1;
  --color-root-tomato-red-light-3: #ffcdcd;
  --color-root-tomato-red-light-4: #fdb3b4;
  --color-root-tomato-red-light-5: #ffa6a9;
  --color-root-tomato-red-light-6: #ff949a;
  --color-root-tomato-red-light-7: #fc7e88;
  --color-root-tomato-red-dark-1: #b80617;
  --color-root-tomato-red-dark-2: #a60001;
  --color-root-tomato-red-dark-3: #960001;
  --color-root-tomato-red-dark-4: #890001;
  --color-root-tomato-red-dark-5: #7d0001;
  --color-root-tomato-red-dark-6: #690117;
  --color-root-tomato-red-dark-7: #57011c;
  --color-root-eggplant-purple-base-1: #a585ff;
  --color-root-eggplant-purple-base-2: #a270ff;
  --color-root-eggplant-purple-base-3: #a15eff;
  --color-root-eggplant-purple-base-4: #9654ff;
  --color-root-eggplant-purple-base-5: #8154ff;
  --color-root-eggplant-purple-base-6: #6f47ff;
  --color-root-eggplant-purple-base-7: #5b3bff;
  --color-root-eggplant-purple-light-1: #f6edff;
  --color-root-eggplant-purple-light-2: #eedeff;
  --color-root-eggplant-purple-light-3: #e6d1ff;
  --color-root-eggplant-purple-light-4: #ddc3fa;
  --color-root-eggplant-purple-light-5: #d2b7f7;
  --color-root-eggplant-purple-light-6: #c3abf7;
  --color-root-eggplant-purple-light-7: #a799f7;
  --color-root-eggplant-purple-dark-1: #6d47e1;
  --color-root-eggplant-purple-dark-2: #5f37d5;
  --color-root-eggplant-purple-dark-3: #5229cc;
  --color-root-eggplant-purple-dark-4: #4d1cb5;
  --color-root-eggplant-purple-dark-5: #3f149a;
  --color-root-eggplant-purple-dark-6: #270275;
  --color-root-eggplant-purple-dark-7: #16003d;
  --color-reserved-true-white: #ffffff;
  --color-reserved-true-black: #000000;
  --color-reserved-durian-yellow-electric: #f2f500;
  --color-surface-100-bg: #ffffff;
  --color-surface-100-fg-default: #07101a;
  --color-surface-100-fg-minor: #52667d;
  --color-surface-100-hairline: #e4eaf5;
  --color-surface-200-bg: #eef2fb;
  --color-surface-200-fg-default: #07101a;
  --color-surface-200-fg-minor: #758296;
  --color-surface-200-hairline: #d3daeb;
  --color-surface-300-bg: #e4eaf5;
  --color-surface-300-fg-default: #07101a;
  --color-surface-300-fg-minor: #758296;
  --color-surface-300-hairline: #c7cede;
  --color-surface-400-bg: #212f3d;
  --color-surface-400-fg-default: #f6f9fc;
  --color-surface-400-fg-minor: #9299ae;
  --color-surface-400-hairline: #3a4c60;
  --color-surface-500-bg: #182432;
  --color-surface-500-fg-default: #f6f9fc;
  --color-surface-500-fg-minor: #9299ae;
  --color-surface-500-hairline: #3a4c60;
  --color-surface-600-bg: #07101a;
  --color-surface-600-fg-default: #f6f9fc;
  --color-surface-600-fg-minor: #9299ae;
  --color-surface-600-hairline: #3a4c60;
  --color-btn-primary-bg: #36c25b;
  --color-btn-primary-fg-default: #ffffff;
  --color-btn-primary-fg-minor: #f6f9fc;
  --color-btn-primary-hairline: #51cdf4;
  --color-btn-primary-behavior-hover: var(--style-filter-lighten-1-hover);
--color-btn-primary-behavior-pressed: var(--style-filter-lighten-1-pressed);
  --color-btn-secondary-bg: #1f4f2d;
  --color-btn-secondary-fg-default: #ffffff;
  --color-btn-secondary-fg-minor: #f6f9fc;
  --color-btn-secondary-hairline: #075ce5;
  --color-btn-secondary-behavior-hover: var(--style-filter-lighten-1-hover);
--color-btn-secondary-behavior-pressed: var(--style-filter-lighten-1-pressed);
  --color-btn-tertiary-bg: #eef2fb;
  --color-btn-tertiary-fg-default: #07101a;
  --color-btn-tertiary-fg-minor: #758296;
  --color-btn-tertiary-hairline: #d3daeb;
  --color-btn-tertiary-behavior-hover: var(--style-filter-darken-1-hover);
--color-btn-tertiary-behavior-pressed: var(--style-filter-darken-1-pressed);
  --color-btn-confirmation-bg: #36c25b;
  --color-btn-confirmation-fg-default: #ffffff;
  --color-btn-confirmation-fg-minor: #f6f9fc;
  --color-btn-confirmation-hairline: #51cdf4;
  --color-btn-confirmation-behavior-hover: var(--style-filter-darken-1-hover);
--color-btn-confirmation-behavior-pressed: var(--style-filter-darken-1-pressed);
  --color-btn-critical-bg: #ec4143;
  --color-btn-critical-fg-default: #ffffff;
  --color-btn-critical-fg-minor: #ffcdcd;
  --color-btn-critical-hairline: #ff7072;
  --color-btn-critical-behavior-hover: var(--style-filter-darken-1-hover);
--color-btn-critical-behavior-pressed: var(--style-filter-darken-1-pressed);
  --color-btn-disabled-bg: #dee4f3;
  --color-btn-disabled-fg-default: #758296;
  --color-btn-disabled-fg-minor: #a1a8bc;
  --color-btn-disabled-hairline: #c7cede;
  --color-tint-white-25: rgba(255, 255, 255, 0.05);
  --color-tint-white-50: rgba(255, 255, 255, 0.1);
  --color-tint-white-100: rgba(255, 255, 255, 0.15);
  --color-tint-white-150: rgba(255, 255, 255, 0.2);
  --color-tint-white-200: rgba(255, 255, 255, 0.25);
  --color-tint-white-250: rgba(255, 255, 255, 0.29);
  --color-tint-white-300: rgba(255, 255, 255, 0.35);
  --color-tint-white-350: rgba(255, 255, 255, 0.4);
  --color-tint-white-400: rgba(255, 255, 255, 0.46);
  --color-tint-white-450: rgba(255, 255, 255, 0.51);
  --color-tint-white-500: rgba(255, 255, 255, 0.54);
  --color-tint-white-550: rgba(255, 255, 255, 0.57);
  --color-tint-white-600: rgba(255, 255, 255, 0.6);
  --color-tint-white-650: rgba(255, 255, 255, 0.65);
  --color-tint-white-700: rgba(255, 255, 255, 0.7);
  --color-tint-white-750: rgba(255, 255, 255, 0.74);
  --color-tint-white-800: rgba(255, 255, 255, 0.78);
  --color-tint-white-850: rgba(255, 255, 255, 0.82);
  --color-tint-white-900: rgba(255, 255, 255, 0.88);
  --color-tint-white-950: rgba(255, 255, 255, 0.93);
  --color-tint-white-1000: #ffffff;
  --color-tint-black-25: rgba(0, 0, 0, 0.05);
  --color-tint-black-50: rgba(0, 0, 0, 0.1);
  --color-tint-black-100: rgba(0, 0, 0, 0.15);
  --color-tint-black-150: rgba(0, 0, 0, 0.2);
  --color-tint-black-200: rgba(0, 0, 0, 0.25);
  --color-tint-black-250: rgba(0, 0, 0, 0.29);
  --color-tint-black-300: rgba(0, 0, 0, 0.35);
  --color-tint-black-350: rgba(0, 0, 0, 0.4);
  --color-tint-black-400: rgba(0, 0, 0, 0.46);
  --color-tint-black-450: rgba(0, 0, 0, 0.51);
  --color-tint-black-500: rgba(0, 0, 0, 0.54);
  --color-tint-black-550: rgba(0, 0, 0, 0.57);
  --color-tint-black-600: rgba(0, 0, 0, 0.6);
  --color-tint-black-650: rgba(0, 0, 0, 0.65);
  --color-tint-black-700: rgba(0, 0, 0, 0.7);
  --color-tint-black-750: rgba(0, 0, 0, 0.74);
  --color-tint-black-800: rgba(0, 0, 0, 0.78);
  --color-tint-black-850: rgba(0, 0, 0, 0.82);
  --color-tint-black-900: rgba(0, 0, 0, 0.88);
  --color-tint-black-950: rgba(0, 0, 0, 0.93);
  --color-tint-black-1000: #000000;
  --color-link-base-1: #027fff;
  --color-success-bg: #d7fae1;
  --color-success-fg: #1f4f2d;
  --color-success-hairline: #8ed4a2;
  --color-success-txt: #329c50;
  --color-critical-bg: #ec4143;
  --color-critical-fg: #ffffff;
  --color-critical-hairline: #b80617;
  --color-critical-txt: #b80617;
  --color-warning-bg: #fffd9d;
  --color-warning-fg: #7d4c02;
  --color-warning-hairline: #ffb700;
  --color-warning-txt: #ab8403;
  --color-highlight-bg: #eedeff;
  --color-highlight-fg: #270275;
  --color-highlight-hairline: #a270ff;
  --color-highlight-txt: #5b3bff;
  --color-pricing-bg: #ec4143;
  --color-pricing-fg: #ffffff;
  --color-pricing-txt: #b80617;
  --color-pricing-hairline: #ffffff;
  --color-atc-mini-bg-default: #ffffff;
  --color-atc-mini-bg-added: #1f4f2d;
  --color-atc-mini-bg-disabled: #dee4f3;
  --color-atc-mini-fg-default: #1f4f2d;
  --color-atc-mini-fg-added: #ffffff;
  --color-atc-mini-fg-disabled: #758296;
  --color-atc-mini-hairline: rgba(0, 165, 36, 0.1);
  --color-atc-large-bg-default: #36c25b;
  --color-atc-large-bg-disabled: #36c25b;
  --color-atc-large-fg-default: #ffffff;
  --color-atc-large-fg-disabled: #2b6633;
  --color-navbar-bg-default: #ffffff;
  --color-navbar-bg-highlight: #ffffff;
  --color-navbar-bg-selected: rgba(2, 127, 255, 0.09);
  --color-navbar-bg-transluscent: rgba(255, 255, 255, 0.88);
  --color-navbar-fg-default: #1f4f2d;
  --color-navbar-fg-highlight: #9654ff;
  --color-navbar-fg-selected: #027fff;
  --color-navbar-hairline: #e4eaf5;
  --color-navbar-divider: rgba(0, 0, 0, 0.1);
  --color-navbar-logo: #1f4f2d;
  --color-sidebar-bg-100: #1f4f2d;
  --color-sidebar-bg-200: #f2f500;
  --color-sidebar-bg-300: #f2f500;
  --color-sidebar-bg-400: #427d48;
  --color-sidebar-fg-100-default: #fafafa;
  --color-sidebar-fg-100-selected: #fafafa;
  --color-sidebar-fg-100-subdued: #a6e7b9;
  --color-sidebar-fg-200: #1f4f2d;
  --color-sidebar-fg-300: #1f4f2d;
  --color-sidebar-fg-400: #fafafa;
  --color-sidebar-hairline: #32874a;
  --color-sidebar-logo: #ffffff;
  --color-backdrop-50-bg: rgba(0, 0, 0, 0.1);
  --color-backdrop-100-bg: rgba(0, 0, 0, 0.78);
  --color-input-100-bg-default: #ffffff;
  --color-input-100-bg-active: #ffffff;
  --color-input-100-bg-disabled: #e2e2e2;
  --color-input-100-bg-critical: #ffeded;
  --color-input-100-fg-default: #07101a;
  --color-input-100-fg-placeholder: #52667d;
  --color-input-100-fg-disabled: #878787;
  --color-input-100-fg-critical: #ec4143;
  --color-input-100-hairline-default: #e4eaf5;
  --color-input-100-hairline-active: #1f4f2d;
  --color-input-100-hairline-disabled: #bbbbbb;
  --color-input-100-hairline-critical: #ec4143;
  --color-input-100-icon-default: #52667d;
  --color-input-100-icon-active: #07101a;
  --color-input-200-bg-default: #eef2fb;
  --color-input-200-bg-active: #ffffff;
  --color-input-200-bg-disabled: #e2e2e2;
  --color-input-200-bg-critical: #ffeded;
  --color-input-200-fg-default: #07101a;
  --color-input-200-fg-placeholder: #52667d;
  --color-input-200-fg-disabled: #878787;
  --color-input-200-fg-critical: #ec4143;
  --color-input-200-hairline-default: #d3daeb;
  --color-input-200-hairline-active: #1f4f2d;
  --color-input-200-hairline-disabled: #bbbbbb;
  --color-input-200-hairline-critical: #ec4143;
  --color-input-200-icon-default: #52667d;
  --color-input-200-icon-active: #07101a;
  --color-notification-fg: #ffffff;
  --color-notification-bg: #ec4143;
  --color-bookmark-bg-selected: #ec4143;
  --color-product-bg-img-tint: rgba(92, 143, 208, 0.06);
  --color-promo-100-fg-default: #ffffff;
  --color-promo-100-fg-minor: #ffd2b8;
  --color-promo-100-fg-hairline: #c02500;
  --color-promo-100-bg-lighter: #ffd2b8;
  --color-promo-100-bg-light: #ff8263;
  --color-promo-100-bg-default: #ff5026;
  --color-promo-100-bg-dark: #e53d14;
  --color-promo-100-bg-darker: #c02500;
  --color-promo-200-fg-default: #af2200;
  --color-promo-200-fg-minor: #e53d14;
  --color-promo-200-fg-hairline: #ffd2b8;
  --color-promo-200-bg-lighter: #ffffff;
  --color-promo-200-bg-light: #feefdb;
  --color-promo-200-bg-default: #ffd2b8;
  --color-promo-200-bg-dark: #ffa08a;
  --color-promo-200-bg-darker: #ffa08a;
  --color-promo-300-fg-default: #000f5d;
  --color-promo-300-fg-minor: #386e3c;
  --color-promo-300-fg-hairline: #a6e7b9;
  --color-promo-300-bg-lighter: #ffffff;
  --color-promo-300-bg-light: #d7fae1;
  --color-promo-300-bg-default: #b6f0c7;
  --color-promo-300-bg-dark: #92e0a9;
  --color-promo-300-bg-darker: #8dcc9f;
  --style-filter-lighten-1-hover: brightness(105%) saturate(105%);
  --style-filter-lighten-1-pressed: brightness(108%) saturate(108%);
  --style-filter-darken-1-hover: brightness(95%) saturate(105%);
  --style-filter-darken-1-pressed: brightness(92%) saturate(108%);
  --style-elevation-1: 0 0 0 1px rgba(0, 0, 0, 0.05);
  --style-elevation-2: 0 2px 6px 0 rgba(0, 0, 0, 0.05);
  --style-elevation-3: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  --style-elevation-4: 0 0 6px 0 rgba(0, 0, 0, 0.05), 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  --style-elevation-5: 0 0 8px 0 rgba(0, 0, 0, 0.05), 0 8px 20px 0 rgba(0, 0, 0, 0.05);
  --style-elevation-6: 0 0 6px 0 rgba(0, 0, 0, 0.05), 0 14px 32px 0 rgba(0, 0, 0, 0.05);
  --style-elevation-7: 0 0 6px 0 rgba(0, 0, 0, 0.05), 0 24px 48px 0 rgba(0, 0, 0, 0.05);
  --style-elevation-8: 0 0 6px 0 rgba(0, 0, 0, 0.05), 0 24px 64px 0 rgba(0, 0, 0, 0.05);
  --style-elevation-9: 0 0 6px 0 rgba(0, 0, 0, 0.05), 0 24px 80px 0 rgba(0, 0, 0, 0.05);
  --size-elevation-distance-100: 1px;
  --size-elevation-distance-200: 2px;
  --size-elevation-distance-300: 6px;
  --size-elevation-distance-400: 8px;
  --size-elevation-distance-500: 10px;
  --size-elevation-distance-600: 12px;
  --size-elevation-distance-700: 14px;
  --size-elevation-distance-800: 16px;
  --size-elevation-distance-900: 18px;
  --size-elevation-distance-1000: 20px;
  --size-elevation-distance-1100: 22px;
  --size-elevation-distance-1200: 24px;
  --size-elevation-blur-100: 0px;
  --size-elevation-blur-200: 4px;
  --size-elevation-blur-300: 6px;
  --size-elevation-blur-400: 8px;
  --size-elevation-blur-500: 12px;
  --size-elevation-blur-600: 16px;
  --size-elevation-blur-700: 20px;
  --size-elevation-blur-800: 24px;
  --size-elevation-blur-900: 28px;
  --size-elevation-blur-1000: 32px;
  --size-elevation-blur-1100: 36px;
  --size-elevation-blur-1200: 40px;
  --size-elevation-blur-1300: 44px;
  --size-elevation-blur-1400: 48px;
  --size-elevation-blur-1800: 64px;
  --size-elevation-blur-2200: 80px;
  --size-elevation-spread-100: 1px;
  --size-elevation-spread-200: 2px;
  --size-elevation-spread-300: 3px;
}
