# node PLATFORM=$npm_config_platform ENVIRONMENT=$npm_config_environment DEVICE=$npm_config_device RANGE=$npm_config_range ./src/build.js 
# ##

node ./src/formatter.js --OUTPUT_DIR=dist/weee/ --TOKEN_SOURCE_DIR=tokens/weee/ --TOKEN_LIB=weee
node ./src/init.js --OUTPUT_DIR=dist/weee/ --TOKEN_SOURCE_DIR=tokens/weee/ --TOKEN_LIB=weee
node ./src/cleanup.js --OUTPUT_DIR=dist/weee/ --TOKEN_SOURCE_DIR=tokens/weee/ --TOKEN_LIB=weee

# node ./src/formatter.js --OUTPUT_DIR=dist/masgusto/ --TOKEN_SOURCE_DIR=tokens/masgusto/ --TOKEN_LIB=masgusto
# node ./src/init.js --OUTPUT_DIR=dist/masgusto/ --TOKEN_SOURCE_DIR=tokens/masgusto/ --TOKEN_LIB=masgusto
# node ./src/cleanup.js --OUTPUT_DIR=dist/masgusto/ --TOKEN_SOURCE_DIR=tokens/masgusto/ --TOKEN_LIB=masgusto

# node ./utils/storybook/storybook-update.js --OUTPUT_DIR=dist/weee/ --TOKEN_SOURCE_DIR=tokens/weee/ --TOKEN_LIB=weee
# node ./utils/storybook/storybook-update.js --OUTPUT_DIR=dist/masgusto/ --TOKEN_SOURCE_DIR=tokens/masgusto/ --TOKEN_LIB=masgusto