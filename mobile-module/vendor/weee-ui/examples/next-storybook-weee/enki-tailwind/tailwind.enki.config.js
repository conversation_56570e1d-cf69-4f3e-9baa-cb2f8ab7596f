
    /**
     * Do not edit directly if you aren't in ~weee-ui/src/tailwind/source/config.js
     * Autogenerated on Mon May 12 2025 18:14:22 GMT-0400 (Eastern Daylight Time), edit in ~weee-ui/src/tailwind/source/config.js
     * IMPORTANT: Be sure to load all CSS style variables required from ~weee-ui/dist/tailwind/css
     */
    

const plugin = require("tailwindcss/plugin");

const colors = require("./styles/color.tailwind");
const size = require("./styles/size.tailwind");
const font = require("./styles/font.tailwind");

const fontClasses = require("./lib/font-classes.tailwind");
const elevationClasses = require("./lib/elevation-classes.tailwind");
const buttonClasses = require("./lib/button-classes.tailwind");
const filterClasses = require("./lib/css-filter-classes.tailwind");

// ___________________________________
// ___________________________________
// ___________________________________
// Default configuration
const enkiTailwindConfig = {
  theme: {
    extend: {
          colors: colors,
          borderRadius: size.radius,
          fontSize: font.size,
          fontWeight: font.weight,
          spacing: size.spacing,
          height: size.spacing,
          width: size.spacing,
          letterSpacing: font.tracking,
          lineHeight: {
              ...font.lineheight,
          },
      },
  },
  plugins: [
      plugin(function ({ addUtilities }) {
          addUtilities({
              ...fontClasses,
              ...elevationClasses,
              ...buttonClasses,
              ...filterClasses
          });
      }),
  ],
};

// ____________________________________
// ____________________________________
// ____________________________________
// Our export function
function withEnkiConfig(tailwindConfig) {
  // ____________________________________
  // Default config
  const defaultConfig = enkiTailwindConfig

  // ____________________________________
  // Deep merge
  function mergeDeep(target, source) {
      const isObject = tailwindConfig => tailwindConfig && typeof tailwindConfig === 'object';

      for (const key in source) {
          const targetValue = target[key];
          const sourceValue = source[key];

          if (Array.isArray(targetValue) && Array.isArray(sourceValue)) {
              target[key] = [...targetValue, ...sourceValue];
          } else if (isObject(targetValue) && isObject(sourceValue)) {
              target[key] = mergeDeep({ ...targetValue }, sourceValue);
          } else {
              target[key] = sourceValue;
          }
      }

      return target;
  }

  return mergeDeep({ ...defaultConfig }, tailwindConfig);
}

// ____________________________________
// ____________________________________
// ____________________________________
// Let's go
module.exports = {
  withEnkiConfig
};
