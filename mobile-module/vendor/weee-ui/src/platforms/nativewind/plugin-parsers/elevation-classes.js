const fs = require("fs");
const { cssToJs } = require("../../../utils/cssToJs");
const { cleanOutputDir } = require("../../../utils/cleanOutputDir");
const { outputJsToFile } = require("../../../utils/outputJsFiles");
const Constants = require("../../../config/constants");


function generateNativeWindElevationPluginClasses(
  outputDir = Constants.V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR
) {
  const date = new Date();
  const fileHeader = `
  /** 
   * Do not edit directly
   * Generated on ${date}
   */
  `;
  const moduleHeader = `module.exports = `;

  cleanOutputDir(outputDir);

  fs.readFile(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_ELEVATION_FILENAME}.css`,
    "utf8",
    function (err, data) {
      let fileData = `${fileHeader}\n\n${moduleHeader}${JSON.stringify(
        cssToJs(data),
        null,
        2
      )}`;

      console.log(`🪄 Creating elevation style library for NativeWind → '${outputDir}'`);

      outputJsToFile(fileData, "elevation-classes", outputDir);
    }
  );
}

module.exports = {
  generateNativeWindElevationPluginClasses,
};