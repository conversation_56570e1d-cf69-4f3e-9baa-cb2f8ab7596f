const Constants = require("../../config/constants");
const StyleDictionary = require("style-dictionary");
const { makeSdTailwindConfig } = require("sd-tailwindcss-transformer");
const fs = require("fs");

const {
  fontWeightTransform,
  webSystemFontFamilyTransform,
  fontTrackingTransform,
  sizePxTransform,
  behaviorStringTransform
} = require("../../utils/transforms");

const { copyFile } = require("node:fs/promises");
const { createTemp, removeTemp } = require("../tailwind/temp"); // 使用 tailwind 定义的方法

function generateNativeWindTheme(
  buildPath = Constants.V2_NATIVEWIND_STYLES_OUTPUT_DIR,
  buildOptions = {
    color: {
      outputReferences: false, // React Native 不支持 CSS 变量
      isVariables: false,
    },
    size: {
      outputReferences: false,
      isVariables: false,
    },
    font: { 
      outputReferences: false, 
      isVariables: false 
    },
  }
) {
  // 注册 React Native 兼容的转换器
  const transforms = {
    [fontWeightTransform("nativewind", true)]: StyleDictionary.registerTransform({
      ...fontWeightTransform("nativewind"),
    }),
    [webSystemFontFamilyTransform("nativewind", true)]:
      StyleDictionary.registerTransform({
        ...webSystemFontFamilyTransform("nativewind"),
      }),
    [fontTrackingTransform("nativewind", true)]:
      StyleDictionary.registerTransform({
        ...fontTrackingTransform("nativewind"),
      }),
    [sizePxTransform("nativewind", true)]: StyleDictionary.registerTransform({
      ...sizePxTransform("nativewind"),
    }),
    [behaviorStringTransform("nativewind", true)]: StyleDictionary.registerTransform({
      ...behaviorStringTransform("nativewind"),
    }),
  };

  const options = {
    source: [Constants.V2_TOKEN_SOURCE_DIR_ALL_CORE_VARIABLES],
    transforms: ["attribute/cti", "name/cti/kebab", ...Object.keys(transforms)],
    buildPath: buildPath,
  };

  const nativewindColorStyles = StyleDictionary.extend(
    makeSdTailwindConfig({
      type: "color",
      ...options,
      ...buildOptions.color,
    })
  );

  const nativewindSizeStyles = StyleDictionary.extend(
    makeSdTailwindConfig({
      type: "size",
      ...options,
      ...buildOptions.size,
    })
  );

  const nativewindFontStyles = StyleDictionary.extend(
    makeSdTailwindConfig({
      type: "font",
      ...options,
      ...buildOptions.font,
    })
  );

  nativewindColorStyles.buildAllPlatforms();
  nativewindSizeStyles.buildAllPlatforms();
  nativewindFontStyles.buildAllPlatforms();
}

function moveConfigToDist(
  fileName,
  outputDir = Constants.V2_NATIVEWIND_OUTPUT_DIR
) {
  console.log(`🔀 Moving NativeWind config '${outputDir + fileName}'`);

  fs.readFile(
    Constants.V2_NATIVEWIND_CONFIG_SOURCE_PATH + fileName,
    "utf8",
    function (err, data) {
      if (err) {
        console.error(`⛔️ Error reading NativeWind config source file '${Constants.V2_NATIVEWIND_CONFIG_SOURCE_PATH + fileName}':`, err);
        // 创建一个基本的配置文件
        const date = new Date();
        const fileHeader = `
    /**
     * Do not edit directly if you aren't in ~weee-ui/src/nativewind/source/config.js
     * Autogenerated on ${date}, edit in ~weee-ui/src/nativewind/source/config.js
     * IMPORTANT: React Native compatible configuration for NativeWind
     */
    `;
        const fallbackData = `undefined`;
        const fileData = `${fileHeader}\n\n${fallbackData}`;

        fs.writeFile(outputDir + fileName, fileData, (writeErr) => {
          if (writeErr) {
            console.error(`⛔️ Error writing fallback file '${outputDir + fileName}'`, writeErr);
          } else {
            console.log(`⚠️ Fallback file '${outputDir + fileName}' has been written`);
          }
        });
        return;
      }

      const date = new Date();
      const fileHeader = `
    /**
     * Do not edit directly if you aren't in ~weee-ui/src/nativewind/source/config.js
     * Autogenerated on ${date}, edit in ~weee-ui/src/nativewind/source/config.js
     * IMPORTANT: React Native compatible configuration for NativeWind
     */
    `;
      const fileData = `${fileHeader}\n\n${data}`;

      fs.writeFile(outputDir + fileName, fileData, (err) => {
        if (err) {
          console.error(`⛔️ Error writing to file '${outputDir + fileName}'`, err);
        } else {
          console.log(`✅ File '${outputDir + fileName}' has been written`);
        }
      });
    }
  );
}

async function buildNativeWindConfig(
  buildPath = Constants.V2_NATIVEWIND_STYLES_OUTPUT_DIR,
  buildOptions = {
    color: {
      outputReferences: false,
      isVariables: false,
    },
    size: {
      outputReferences: false,
      isVariables: false,
    },
    font: { 
      outputReferences: false, 
      isVariables: false 
    },
  },
  outputDir = Constants.V2_NATIVEWIND_OUTPUT_DIR
) {
  console.log("");
  console.log("🚧 Building temporary NativeWind theme");
  await createTemp();
  generateNativeWindTheme(buildPath, buildOptions);

  console.log("");
  console.log("🔥 Removing temporary theme");
  await removeTemp();

  console.log("");
  console.log(`📥 Moving NativeWind config.js to '${outputDir}'`);
  await moveConfigToDist(Constants.V2_NATIVEWIND_CONFIG_FILENAME, outputDir);
}

module.exports = {
  buildNativeWindConfig,
};