// _________________________________
// Core
const Constants = require("./config/constants");
const StyleDictionary = require("style-dictionary");
const fs = require("fs");

// _________________________________
// Parsers
const {
  buildTailwindConfig,
  copyTailwindCssFiles,
} = require("./platforms/tailwind");
const { buildTailwindFontVariables } = require("./platforms/tailwind/fonts.js");
const { buildCssFontStyles } = require("./platforms/web/fonts.js");
const { buildCssElevationStyles } = require("./platforms/web/elevation.js");
const { buildCssFilterStyles } = require("./platforms/web/cssFilters.js");
const { buildCssButtonStyles } = require("./platforms/web/buttons.js");
const {
  generateTailwindFontPluginClasses,
} = require("./platforms/tailwind/plugin-parsers/font-classes.js");
const {
  generateTailwindElevationPluginClasses,
} = require("./platforms/tailwind/plugin-parsers/elevation-classes.js");
const {
  generateTailwindButtonPluginClasses,
} = require("./platforms/tailwind/plugin-parsers/button-classes.js");
const { generateTailwindCssFilterPluginClasses } = require("./platforms/tailwind/plugin-parsers/cssFilter-classes.js");
const { copyAndroidFontFiles } = require("./platforms/android");


const { buildNativeWindConfig } = require("./platforms/nativewind");
const {
  generateNativeWindFontPluginClasses,
} = require("./platforms/nativewind/plugin-parsers/font-classes.js");
const {
  generateNativeWindButtonPluginClasses,
} = require("./platforms/nativewind/plugin-parsers/button-classes.js");
const {
  generateNativeWindElevationPluginClasses,
} = require("./platforms/nativewind/plugin-parsers/elevation-classes.js");

// _________________________________
// Platforms
const platforms = {
  web: require("./platforms/web"),
  json: require("./platforms/json"),
  js: require("./platforms/js-module"),
  swift: require("./platforms/swift"),
  android: require("./platforms/android"),
  // tailwind: require("./platforms/tailwind/tailwindFonts/index.js"),
};

const createPlatformsList = (
  filename = "variables",
  subdirectory = "",
  exclude = [""],
  cssSelector = "",
  swiftClassName = ""
) => {
  const scss = !exclude.includes("scss")
    ? platforms.web.generateFiles(
        "scss/variables",
        "scss",
        filename,
        cssSelector,
        subdirectory
      )
    : {};

  const less = !exclude.includes("less") && Constants.ENABLE_LESS_EXPORT
    ? platforms.web.generateFiles(
        "less/variables",
        "less",
        filename,
        cssSelector,
        subdirectory
      )
    : {};

  const css = !exclude.includes("css")
    ? platforms.web.generateFiles(
        "css/variables",
        "css",
        filename,
        cssSelector,
        subdirectory
      )
    : {};

  const swift = !exclude.includes("swift")
    ? platforms.swift.generateFiles(
        "ios-swift/any.swift",
        "swift",
        filename,
        swiftClassName,
        subdirectory
      )
    : {};

  const android = !exclude.includes("android")
    ? platforms.android.generateFiles(
        "ios-swift/any.swift",
        "swift",
        filename,
        swiftClassName,
        subdirectory
      )
    : {};

  return {
    scss,
    less,
    css,
    swift,
    android,
  };
};

// _________________________________
// _________________________________
// _________________________________
// Build scripts

// __________________
// [COLORS] Light mode
const defaultColorStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_COLORS_WITH_ELEVATION],
  platforms: createPlatformsList(
    Constants.V2_COLORS_FILENAME,
    Constants.V2_SOURCE_SUBDIRECTORY,
    ["swift"],
    "",
    Constants.V2_SWIFT_COLORS_SELECTOR
  ),
});

const defaultColorStylesNoRef = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_COLORS_WITH_ELEVATION],
  platforms: {
    css: platforms.web.generateFiles(
      "css/variables",
      "css",
      Constants.V2_COLORS_FILENAME,
      "",
      `/${Constants.V2_NO_REF_DIRECTORY}`,
      true
    ),
  },
});

const swiftDefaultColorStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_COLORS],
  platforms: createPlatformsList(
    Constants.V2_COLORS_FILENAME,
    Constants.V2_SOURCE_SUBDIRECTORY,
    ["scss", "css", "less", "android"],
    "",
    Constants.V2_SWIFT_COLORS_SELECTOR
  ),
});

// __________________
// [COLORS] Dark mode
const darkColorStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_COLORS_DARK],
  platforms: createPlatformsList(
    Constants.V2_COLORS_DARK_FILENAME,
    "",
    [""],
    Constants.V2_CSS_COLORS_DARK_SELECTOR,
    Constants.V2_SWIFT_COLORS_DARK_SELECTOR
  ),
});

// __________________
// [SIZE] All size variables
const sizeStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_SIZE],
  platforms: createPlatformsList(
    Constants.V2_SIZE_FILENAME,
    Constants.V2_SOURCE_SUBDIRECTORY,
    ["swift"],
    "",
    Constants.V2_SWIFT_SIZE_SELECTOR
  ),
});

const swiftSizeStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_SIZE_WITH_ELEVATION],
  platforms: createPlatformsList(
    Constants.V2_SIZE_FILENAME,
    Constants.V2_SOURCE_SUBDIRECTORY,
    [""],
    "",
    Constants.V2_SWIFT_SIZE_SELECTOR
  ),
});

// __________________
// [TYPOGRAPHY-WEB] CJK typography
const cjkTypographyStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_CJK],
  platforms: createPlatformsList(
    Constants.V2_TYPOGRAPHY_CJK_FILENAME,
    Constants.V2_SOURCE_SUBDIRECTORY,
    ["swift", "android"],
    "",
    Constants.V2_SWIFT_TYPOGRAPHY_CJK_SELECTOR
  ),
});

// __________________
// [TYPOGRAPHY-WEB] Latin typography
const latinTypographyStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_LATIN],
  platforms: createPlatformsList(
    Constants.V2_TYPOGRAPHY_LATIN_FILENAME,
    Constants.V2_SOURCE_SUBDIRECTORY,
    ["swift", "android"],
    "",
    Constants.V2_SWIFT_TYPOGRAPHY_LATIN_SELECTOR
  ),
});

// __________________
// [TYPOGRAPHY-WEB] Tall (Vietnamese) typography
const tallTypographyStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_TALL],
  platforms: createPlatformsList(
    Constants.V2_TYPOGRAPHY_TALL_FILENAME,
    Constants.V2_SOURCE_SUBDIRECTORY,
    ["swift", "android"],
    "",
    Constants.V2_SWIFT_TYPOGRAPHY_TALL_SELECTOR
  ),
});

// __________________
// [TYPOGRAPHY-SWIFT] All typography
const swiftTypographyStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_COMPLETE_VARIABLES],
  platforms: createPlatformsList(
    Constants.V2_TYPOGRAPHY_FILENAME,
    "",
    ["android", "scss", "less", "css"],
    "",
    Constants.V2_SWIFT_TYPOGRAPHY_CORE_SELECTOR
  ),
});

// __________________
// [TYPOGRAPHY-ANDROID] Typography font styles
const androidTypographyFontStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_COMPLETE_VARIABLES],
  platforms: createPlatformsList(
    Constants.V2_TYPOGRAPHY_FILENAME,
    "",
    ["swift", "scss", "less", "css"],
    "",
    ""
  ),
});

// __________________
// [BUNDLE-WEB] CJK Styles
const cjkBundleStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_BUNDLE_CJK],
  platforms: createPlatformsList(
    Constants.V2_CJK_BUNDLE_FILENAME,
    "",
    ["swift", "android"],
    "",
    Constants.V2_SWIFT_CJK_BUNDLE_SELECTOR
  ),
});

// __________________
// [BUNDLE-WEB] Latin Styles
const latinBundleStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_BUNDLE_LATIN],
  platforms: createPlatformsList(
    Constants.V2_LATIN_BUNDLE_FILENAME,
    "",
    ["swift", "android"],
    "",
    Constants.V2_SWIFT_LATIN_BUNDLE_SELECTOR
  ),
});

// __________________
// [BUNDLE-WEB] Latin Tall Styles
const tallBundleStyles = StyleDictionary.extend({
  source: [Constants.V2_TOKEN_SOURCE_DIR_BUNDLE_TALL],
  platforms: createPlatformsList(
    Constants.V2_TALL_BUNDLE_FILENAME,
    "",
    ["swift", "android"],
    "",
    Constants.V2_SWIFT_TALL_BUNDLE_SELECTOR
  ),
});

// __________________
// Clean output directory
function cleanGeneratedOutputDir() {
  const dirPath = Constants.OUTPUT_DIR;

  // Check if the directory exists
  if (fs.existsSync(dirPath)) {
    console.log(`⚠️ Directory '${dirPath}' exists. Removing for fresh tokens.`);

    // Remove the directory and its contents
    fs.rmSync(dirPath, { recursive: true });
    console.log(`🔥 Directory '${dirPath}' removed.`);
  }

  // Create the new directory and any parent directories that don't exist
  fs.mkdirSync(dirPath, { recursive: true });
  console.log(`🐣 Empty directory '${dirPath}' created.`);
}

// _________________________________
// _________________________________
// _________________________________
// Build all platforms

async function buildEnkiTokenLibrary() {
  // ________________________________
  // Clean up output directory
  console.log("");
  console.log("╔════════════════════════════════════╗");
  console.log("║ 🧼 Cleaning up output directory... ║");
  console.log("╚════════════════════════════════════╝");
  cleanGeneratedOutputDir();

  // ________________________________
  // ________________________________
  // ________________________________
  // Build all platforms with Style Dictionary

  /* Be sure to loasd this first as it is destructive */
  console.log("");
  console.log("╔══════════════════════════════════════════════════╗");
  console.log("║ 💨 [WEB-TYPOGRAPHY] Building CSS font styles...  ║");
  console.log("╚══════════════════════════════════════════════════╝");
  buildCssFontStyles();
  /* ----------------------------------------------- */

  console.log("");
  console.log("╔═══════════════════════════════════════════╗");
  console.log("║ 🌝 [COLORS] Building light mode styles... ║");
  console.log("╚═══════════════════════════════════════════╝");
  defaultColorStyles.buildAllPlatforms();
  defaultColorStylesNoRef.buildAllPlatforms();
  swiftDefaultColorStyles.buildAllPlatforms();

  console.log("");
  console.log("╔════════════════════════════════════════════╗");
  console.log("║  🌚 [COLORS] Building dark mode styles...  ║");
  console.log("╚════════════════════════════════════════════╝");
  darkColorStyles.buildAllPlatforms();

  console.log("");
  console.log("╔════════════════════════════════════════════════════════╗");
  console.log("║  📐 [SIZE] Building sizing & measurements styles...  ║");
  console.log("╚════════════════════════════════════════════════════════╝");
  sizeStyles.buildAllPlatforms();
  swiftSizeStyles.buildAllPlatforms();

  console.log("");
  console.log("╔═════════════════════════════════════════════════════════╗");
  console.log("║  🔠 [WEB-TYPOGRAPHY] Building CJK typography styles...  ║");
  console.log("╚═════════════════════════════════════════════════════════╝");
  cjkTypographyStyles.buildAllPlatforms();

  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════╗");
  console.log("║  🔠 [WEB-TYPOGRAPHY] Building Latin typography styles...  ║");
  console.log("╚═══════════════════════════════════════════════════════════╝");
  latinTypographyStyles.buildAllPlatforms();

  console.log("");
  console.log(
    "╔════════════════════════════════════════════════════════════════╗"
  );
  console.log(
    "║  🔠 [WEB-TYPOGRAPHY] Building Latin Tall typography styles...  ║"
  );
  console.log(
    "╚════════════════════════════════════════════════════════════════╝"
  );
  tallTypographyStyles.buildAllPlatforms();

  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════╗");
  console.log("║  🔠 [SWIFT-TYPOGRAPHY] Building all typography styles...  ║");
  console.log("╚═══════════════════════════════════════════════════════════╝");
  swiftTypographyStyles.buildAllPlatforms();

  console.log("");
  console.log(
    "╔═════════════════════════════════════════════════════════════╗"
  );
  console.log(
    "║  🔠 [ANDROID-TYPOGRAPHY] Building all typography styles...  ║"
  );
  console.log(
    "╚═════════════════════════════════════════════════════════════╝"
  );
  androidTypographyFontStyles.buildAllPlatforms();

  console.log("");
  console.log("╔═════════════════════════════════════════════════╗");
  console.log("║  📦 [WEB-BUNDLE] Building CJK styles bundle...  ║");
  console.log("╚═════════════════════════════════════════════════╝");
  cjkBundleStyles.buildAllPlatforms();

  console.log("");
  console.log("╔═══════════════════════════════════════════════════╗");
  console.log("║  📦 [WEB-BUNDLE] Building Latin styles bundle...  ║");
  console.log("╚═══════════════════════════════════════════════════╝");
  latinBundleStyles.buildAllPlatforms();

  console.log("");
  console.log("╔════════════════════════════════════════════════════════╗");
  console.log("║  📦 [WEB-BUNDLE] Building Latin Tall styles bundle...  ║");
  console.log("╚════════════════════════════════════════════════════════╝");
  tallBundleStyles.buildAllPlatforms();

  console.log("");
  console.log("╔════════════════════════════════════════════════════╗");
  console.log("║  📦 [WEB-ELEVATION] Building elevation classes...  ║");
  console.log("╚════════════════════════════════════════════════════╝");
  buildCssElevationStyles();

  console.log("");
  console.log("╔═════════════════════════════════════════════════╗");
  console.log("║  📦 [WEB-ELEVATION] Building filter classes...  ║");
  console.log("╚═════════════════════════════════════════════════╝");
  buildCssFilterStyles();

  console.log("");
  console.log("╔═══════════════════════════════════════════════╗");
  console.log("║  📦 [WEB-BUTTONS] Building button classes...  ║");
  console.log("╚═══════════════════════════════════════════════╝");
  buildCssButtonStyles();

  console.log("");
  console.log("╔═════════════════════════════════════════════════════╗");
  console.log("║ 💨 [TAILWIND-CSS-VARS] Building Tailwind config...  ║");
  console.log("╚═════════════════════════════════════════════════════╝");
  await buildTailwindConfig();

  console.log("");
  console.log(
    "╔═══════════════════════════════════════════════════════════════════╗"
  );
  console.log(
    "║ 💨 [TAILWIND-CSS-VARS-TYPOGRAPHY] Building CSS font variables...  ║"
  );
  console.log(
    "╚═══════════════════════════════════════════════════════════════════╝"
  );
  buildTailwindFontVariables();

  console.log("");
  console.log("╔═════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-STYLES] Copying CSS variables...  ║");
  console.log("╚═════════════════════════════════════════════════════════╝");
  await copyTailwindCssFiles(Constants.V2_TAILWIND_CSS_OUTPUT_DIR);

  console.log("");
  console.log("╔═════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-PLUGIN] Creating Tailwind font plugin classes...  ║");
  console.log("╚═════════════════════════════════════════════════════════════════════════╝");
  generateTailwindFontPluginClasses();

  console.log("");
  console.log("");
  console.log("╔══════════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-PLUGIN] Creating Tailwind elevation plugin classes...  ║");
  console.log("╚══════════════════════════════════════════════════════════════════════════════╝");
  generateTailwindElevationPluginClasses();

  console.log("");
  console.log("");
  console.log("╔════════════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-PLUGIN] Creating Tailwind css filters plugin classes...  ║");
  console.log("╚════════════════════════════════════════════════════════════════════════════════╝");
  generateTailwindCssFilterPluginClasses();

  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-PLUGIN] Creating Tailwind button plugin classes...  ║");
  console.log("╚═══════════════════════════════════════════════════════════════════════════╝");
  generateTailwindButtonPluginClasses();

  console.log("");
  console.log("╔═════════════════════════════════════════════════════╗");
  console.log("║ 💨 [TAILWIND-EMBEDDED] Building Tailwind config...  ║");
  console.log("╚═════════════════════════════════════════════════════╝");
  await buildTailwindConfig(
    Constants.V2_TAILWIND_EMBEDDED_STYLES_OUTPUT_DIR,
    {
      color: {
        outputReferences: false,
        isVariables: false,
      },
      size: {},
      font: { outputReferences: false, isVariables: false },
    },
    Constants.V2_TAILWIND_EMBEDDED_OUTPUT_DIR
  );

  console.log("");
  console.log(
    "╔═══════════════════════════════════════════════════════════════════╗"
  );
  console.log(
    "║ 💨 [TAILWIND-EMBEDDED-TYPOGRAPHY] Building CSS font variables...  ║"
  );
  console.log(
    "╚═══════════════════════════════════════════════════════════════════╝"
  );
  buildTailwindFontVariables(Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR);

  console.log("");
  console.log("╔═════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-EMBEDDED-STYLES] Copying CSS variables...  ║");
  console.log("╚═════════════════════════════════════════════════════════╝");
  await copyTailwindCssFiles(
    Constants.V2_TAILWIND_EMBEDDED_CSS_OUTPUT_DIR,
    false
  );

  console.log("");
  console.log("╔═════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-PLUGIN] Creating Tailwind font plugin classes...  ║");
  console.log("╚═════════════════════════════════════════════════════════════════════════╝");
  generateTailwindFontPluginClasses(Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR);

  console.log("");
  console.log("");
  console.log("╔══════════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-PLUGIN] Creating Tailwind elevation plugin classes...  ║");
  console.log("╚══════════════════════════════════════════════════════════════════════════════╝");
  generateTailwindElevationPluginClasses(Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR);

  console.log("");
  console.log("");
  console.log("╔════════════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-PLUGIN] Creating Tailwind css filters plugin classes...  ║");
  console.log("╚════════════════════════════════════════════════════════════════════════════════╝");
  generateTailwindCssFilterPluginClasses(Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR);

  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [TAILWIND-CSS-VARS-PLUGIN] Creating Tailwind button plugin classes...  ║");
  console.log("╚═══════════════════════════════════════════════════════════════════════════╝");
  generateTailwindButtonPluginClasses(Constants.V2_TAILWIND_EMBEDDED_PLUGIN_JS_OUTPUT_DIR);


  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📥 [ANDROID] Copying font files...  ║");
  console.log("╚═══════════════════════════════════════════════════════════════════════════╝");
  copyAndroidFontFiles();

  // buildEnkiTokenNativeWind();
}




async function buildEnkiTokenNativeWind() {

  console.log("");
  console.log("╔═════════════════════════════════════════════════════╗");
  console.log("║ 📱 [NATIVEWIND] Building NativeWind config...       ║");
  console.log("╚═════════════════════════════════════════════════════╝");
  await buildNativeWindConfig(
    Constants.V2_NATIVEWIND_STYLES_OUTPUT_DIR,
    {
      color: {
        outputReferences: false,
        isVariables: false,
      },
      size: {
        outputReferences: false,
        isVariables: false,
      },
      font: { 
        outputReferences: false, 
        isVariables: false 
      },
    },
    Constants.V2_NATIVEWIND_OUTPUT_DIR
  );

  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📱 [NATIVEWIND] Creating NativeWind font plugin classes...               ║");
  console.log("╚═══════════════════════════════════════════════════════════════════════════╝");
  generateNativeWindFontPluginClasses(Constants.V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR);

  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📱 [NATIVEWIND] Creating NativeWind button plugin classes...             ║");
  console.log("╚═══════════════════════════════════════════════════════════════════════════╝");
  generateNativeWindButtonPluginClasses(Constants.V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR);

  console.log("");
  console.log("╔═══════════════════════════════════════════════════════════════════════════╗");
  console.log("║ 📱 [NATIVEWIND] Creating NativeWind evelation plugin classes...             ║");
  console.log("╚═══════════════════════════════════════════════════════════════════════════╝");
  generateNativeWindElevationPluginClasses(Constants.V2_NATIVEWIND_PLUGIN_JS_OUTPUT_DIR);

}

module.exports = {
  buildEnkiTokenLibrary
};
